<?php

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/YouTubeLink.php';
require_once __DIR__ . '/../models/YouTubeLinkCollection.php';

class YouTubeLinkController extends BaseController {
    private $youtubeLinkModel;
    private $collectionModel;

    public function __construct() {
        $this->youtubeLinkModel = new YouTubeLink();
        $this->collectionModel = new YouTubeLinkCollection();
    }

    /**
     * Display YouTube Links dashboard
     */
    public function index() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get collections with stats
        $collections = $this->collectionModel->getUserCollections($userId);
        
        // Get recent links
        $recentLinks = $this->youtubeLinkModel->getRecentLinks($userId, 10);
        
        // Get user statistics
        $stats = $this->youtubeLinkModel->getUserStats($userId);
        
        // Get collection summary
        $collectionSummary = $this->collectionModel->getCollectionSummary($userId);

        $this->view('tools/youtube_links/index', [
            'title' => 'YouTube Links',
            'collections' => $collections,
            'recentLinks' => $recentLinks,
            'stats' => $stats,
            'collectionSummary' => $collectionSummary
        ]);
    }

    /**
     * Add new YouTube link
     */
    public function addLink() {
        $this->requireLogin();
        
        if ($this->isPost()) {
            $user = Session::getUser();
            $userId = $user['id'];

            $data = [
                'url' => trim($_POST['url'] ?? ''),
                'title' => trim($_POST['title'] ?? ''),
                'collection_id' => !empty($_POST['collection_id']) ? intval($_POST['collection_id']) : null,
                'priority' => $_POST['priority'] ?? 'medium',
                'personal_notes' => trim($_POST['personal_notes'] ?? ''),
                'tags' => !empty($_POST['tags']) ? explode(',', $_POST['tags']) : [],
                'added_from' => 'manual'
            ];

            // Validate URL
            if (empty($data['url'])) {
                Session::setFlash('error', 'URL is required');
                $this->redirect('/momentum/tools/youtube-links');
                return;
            }

            // If no collection specified, use default
            if (!$data['collection_id']) {
                $defaultCollection = $this->collectionModel->getDefaultCollection($userId);
                $data['collection_id'] = $defaultCollection['id'] ?? null;
            }

            $linkId = $this->youtubeLinkModel->addLink($userId, $data);

            if ($linkId) {
                Session::setFlash('success', 'YouTube link added successfully');
            } else {
                Session::setFlash('error', 'Failed to add YouTube link');
            }

            $this->redirect('/momentum/tools/youtube-links');
        } else {
            // Show add form
            $user = Session::getUser();
            $userId = $user['id'];
            $collections = $this->collectionModel->getUserCollections($userId);

            $this->view('tools/youtube_links/add', [
                'title' => 'Add YouTube Link',
                'collections' => $collections
            ]);
        }
    }

    /**
     * Quick add link (AJAX)
     */
    public function quickAdd() {
        $this->requireLogin();
        
        if (!$this->isAjax() || !$this->isPost()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data['url'])) {
            $this->json(['success' => false, 'message' => 'URL is required'], 400);
            return;
        }

        // Use default collection if none specified
        if (empty($data['collection_id'])) {
            $defaultCollection = $this->collectionModel->getDefaultCollection($userId);
            $data['collection_id'] = $defaultCollection['id'] ?? null;
        }

        $linkData = [
            'url' => trim($data['url']),
            'title' => trim($data['title'] ?? ''),
            'collection_id' => $data['collection_id'],
            'priority' => $data['priority'] ?? 'medium',
            'personal_notes' => trim($data['notes'] ?? ''),
            'added_from' => 'quick_add'
        ];

        $linkId = $this->youtubeLinkModel->addLink($userId, $linkData);

        if ($linkId) {
            $this->json(['success' => true, 'message' => 'Link added successfully', 'link_id' => $linkId]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to add link'], 500);
        }
    }

    /**
     * Update link status
     */
    public function updateStatus() {
        $this->requireLogin();
        
        if (!$this->isAjax() || !$this->isPost()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data['link_id']) || empty($data['status'])) {
            $this->json(['success' => false, 'message' => 'Link ID and status are required'], 400);
            return;
        }

        $result = $this->youtubeLinkModel->updateProgress(
            $data['link_id'], 
            $userId, 
            $data['status'], 
            $data['progress'] ?? null
        );

        if ($result) {
            $this->json(['success' => true, 'message' => 'Status updated successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update status'], 500);
        }
    }

    /**
     * View collection
     */
    public function viewCollection($collectionId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get collection with stats
        $collection = $this->collectionModel->getCollectionWithStats($collectionId, $userId);
        
        if (!$collection) {
            Session::setFlash('error', 'Collection not found');
            $this->redirect('/momentum/tools/youtube-links');
            return;
        }

        // Get links in collection
        $links = $this->youtubeLinkModel->getUserLinks($userId, ['collection_id' => $collectionId]);

        $this->view('tools/youtube_links/collection', [
            'title' => $collection['name'],
            'collection' => $collection,
            'links' => $links
        ]);
    }

    /**
     * Search links
     */
    public function search() {
        $this->requireLogin();
        
        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $query = $_GET['q'] ?? '';
        
        if (empty($query)) {
            $this->json(['success' => false, 'message' => 'Search query is required'], 400);
            return;
        }

        $links = $this->youtubeLinkModel->searchLinks($userId, $query);

        $this->json(['success' => true, 'links' => $links]);
    }

    /**
     * Get dashboard data (AJAX)
     */
    public function getDashboardData() {
        $this->requireLogin();
        
        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = [
            'stats' => $this->youtubeLinkModel->getUserStats($userId),
            'recentLinks' => $this->youtubeLinkModel->getRecentLinks($userId, 5),
            'collections' => $this->collectionModel->getUserCollections($userId)
        ];

        $this->json(['success' => true, 'data' => $data]);
    }

    /**
     * Bulk operations
     */
    public function bulkAction() {
        $this->requireLogin();
        
        if (!$this->isAjax() || !$this->isPost()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data['link_ids']) || empty($data['action'])) {
            $this->json(['success' => false, 'message' => 'Link IDs and action are required'], 400);
            return;
        }

        $linkIds = $data['link_ids'];
        $action = $data['action'];

        switch ($action) {
            case 'mark_completed':
                $result = $this->youtubeLinkModel->bulkUpdate($linkIds, $userId, ['status' => 'completed']);
                break;
            case 'mark_to_watch':
                $result = $this->youtubeLinkModel->bulkUpdate($linkIds, $userId, ['status' => 'to_watch']);
                break;
            case 'set_high_priority':
                $result = $this->youtubeLinkModel->bulkUpdate($linkIds, $userId, ['priority' => 'high']);
                break;
            case 'move_to_collection':
                if (empty($data['collection_id'])) {
                    $this->json(['success' => false, 'message' => 'Collection ID is required'], 400);
                    return;
                }
                $result = $this->youtubeLinkModel->bulkUpdate($linkIds, $userId, ['collection_id' => $data['collection_id']]);
                break;
            default:
                $this->json(['success' => false, 'message' => 'Invalid action'], 400);
                return;
        }

        if ($result) {
            $this->json(['success' => true, 'message' => 'Bulk action completed successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to complete bulk action'], 500);
        }
    }
}
