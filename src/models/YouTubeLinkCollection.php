<?php

require_once __DIR__ . '/BaseModel.php';

class YouTubeLinkCollection extends BaseModel {
    protected $table = 'youtube_link_collections';

    /**
     * Get user's collections
     */
    public function getUserCollections($userId) {
        $sql = "SELECT ylc.*, 
                       COUNT(yl.id) as link_count,
                       SUM(CASE WHEN yl.status = 'to_watch' THEN 1 ELSE 0 END) as to_watch_count,
                       SUM(CASE WHEN yl.status = 'completed' THEN 1 ELSE 0 END) as completed_count
                FROM {$this->table} ylc
                LEFT JOIN youtube_links yl ON ylc.id = yl.collection_id
                WHERE ylc.user_id = ?
                GROUP BY ylc.id
                ORDER BY ylc.sort_order ASC, ylc.created_at ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get default collection for user
     */
    public function getDefaultCollection($userId) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ? AND is_default = TRUE LIMIT 1";
        return $this->db->fetchOne($sql, [$userId]);
    }

    /**
     * Create a new collection
     */
    public function createCollection($userId, $data) {
        $collectionData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'description' => $data['description'] ?? '',
            'color' => $data['color'] ?? '#3B82F6',
            'icon' => $data['icon'] ?? 'fas fa-folder',
            'sort_order' => $data['sort_order'] ?? $this->getNextSortOrder($userId),
            'is_default' => $data['is_default'] ?? false,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // If this is being set as default, unset other defaults
        if ($collectionData['is_default']) {
            $this->unsetDefaultCollections($userId);
        }

        return $this->create($collectionData);
    }

    /**
     * Update collection
     */
    public function updateCollection($collectionId, $userId, $data) {
        // Verify ownership
        $collection = $this->find($collectionId);
        if (!$collection || $collection['user_id'] != $userId) {
            return false;
        }

        $updateData = [
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Only update provided fields
        $allowedFields = ['name', 'description', 'color', 'icon', 'sort_order', 'is_default'];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        // If this is being set as default, unset other defaults
        if (isset($data['is_default']) && $data['is_default']) {
            $this->unsetDefaultCollections($userId);
        }

        return $this->update($collectionId, $updateData);
    }

    /**
     * Delete collection and move links to default
     */
    public function deleteCollection($collectionId, $userId) {
        // Verify ownership
        $collection = $this->find($collectionId);
        if (!$collection || $collection['user_id'] != $userId) {
            return false;
        }

        // Don't allow deleting the default collection
        if ($collection['is_default']) {
            return false;
        }

        // Get default collection
        $defaultCollection = $this->getDefaultCollection($userId);
        if (!$defaultCollection) {
            return false;
        }

        // Move all links to default collection
        $sql = "UPDATE youtube_links SET collection_id = ? WHERE collection_id = ? AND user_id = ?";
        $this->db->execute($sql, [$defaultCollection['id'], $collectionId, $userId]);

        // Delete the collection
        return $this->delete($collectionId);
    }

    /**
     * Get next sort order for user
     */
    private function getNextSortOrder($userId) {
        $sql = "SELECT MAX(sort_order) as max_order FROM {$this->table} WHERE user_id = ?";
        $result = $this->db->fetchOne($sql, [$userId]);
        return ($result['max_order'] ?? 0) + 1;
    }

    /**
     * Unset default flag for all user collections
     */
    private function unsetDefaultCollections($userId) {
        $sql = "UPDATE {$this->table} SET is_default = FALSE WHERE user_id = ?";
        return $this->db->execute($sql, [$userId]);
    }

    /**
     * Reorder collections
     */
    public function reorderCollections($userId, $collectionOrders) {
        $success = true;
        
        foreach ($collectionOrders as $collectionId => $sortOrder) {
            $result = $this->updateCollection($collectionId, $userId, ['sort_order' => $sortOrder]);
            if (!$result) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Get collection with link statistics
     */
    public function getCollectionWithStats($collectionId, $userId) {
        $sql = "SELECT ylc.*, 
                       COUNT(yl.id) as total_links,
                       SUM(CASE WHEN yl.status = 'to_watch' THEN 1 ELSE 0 END) as to_watch_count,
                       SUM(CASE WHEN yl.status = 'watching' THEN 1 ELSE 0 END) as watching_count,
                       SUM(CASE WHEN yl.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                       SUM(CASE WHEN yl.priority = 'high' THEN 1 ELSE 0 END) as high_priority_count
                FROM {$this->table} ylc
                LEFT JOIN youtube_links yl ON ylc.id = yl.collection_id
                WHERE ylc.id = ? AND ylc.user_id = ?
                GROUP BY ylc.id";

        return $this->db->fetchOne($sql, [$collectionId, $userId]);
    }

    /**
     * Create default collections for new user
     */
    public function createDefaultCollections($userId) {
        $defaultCollections = [
            [
                'name' => 'Watch Later',
                'description' => 'Videos to watch when you have time',
                'color' => '#3B82F6',
                'icon' => 'fas fa-clock',
                'sort_order' => 0,
                'is_default' => true
            ],
            [
                'name' => 'Learning',
                'description' => 'Educational and tutorial videos',
                'color' => '#10B981',
                'icon' => 'fas fa-graduation-cap',
                'sort_order' => 1,
                'is_default' => false
            ],
            [
                'name' => 'Business Ideas',
                'description' => 'Videos about business opportunities and strategies',
                'color' => '#F59E0B',
                'icon' => 'fas fa-lightbulb',
                'sort_order' => 2,
                'is_default' => false
            ]
        ];

        $created = [];
        foreach ($defaultCollections as $collection) {
            $collectionId = $this->createCollection($userId, $collection);
            if ($collectionId) {
                $created[] = $collectionId;
            }
        }

        return $created;
    }

    /**
     * Get collection summary for dashboard
     */
    public function getCollectionSummary($userId) {
        $sql = "SELECT 
                    COUNT(DISTINCT ylc.id) as total_collections,
                    COUNT(yl.id) as total_links,
                    SUM(CASE WHEN yl.status = 'to_watch' THEN 1 ELSE 0 END) as to_watch_total,
                    SUM(CASE WHEN yl.status = 'completed' THEN 1 ELSE 0 END) as completed_total,
                    SUM(CASE WHEN yl.priority = 'high' THEN 1 ELSE 0 END) as high_priority_total
                FROM {$this->table} ylc
                LEFT JOIN youtube_links yl ON ylc.id = yl.collection_id
                WHERE ylc.user_id = ?";

        $result = $this->db->fetchOne($sql, [$userId]);
        
        return [
            'total_collections' => intval($result['total_collections']),
            'total_links' => intval($result['total_links']),
            'to_watch_total' => intval($result['to_watch_total']),
            'completed_total' => intval($result['completed_total']),
            'high_priority_total' => intval($result['high_priority_total'])
        ];
    }
}
