<?php

require_once __DIR__ . '/BaseModel.php';

class YouTubeLink extends BaseModel {
    protected $table = 'youtube_links';

    /**
     * Add a new YouTube link
     */
    public function addLink($userId, $data) {
        // Extract YouTube ID from URL if provided
        $youtubeId = $this->extractYouTubeId($data['url']);
        
        // Get metadata if URL is valid YouTube URL
        $metadata = [];
        if ($youtubeId) {
            $metadata = $this->extractMetadata($data['url']);
        }

        $linkData = [
            'user_id' => $userId,
            'collection_id' => $data['collection_id'] ?? null,
            'youtube_id' => $youtubeId,
            'url' => $data['url'],
            'title' => $data['title'] ?? $metadata['title'] ?? '',
            'channel_name' => $data['channel_name'] ?? $metadata['channel_name'] ?? '',
            'duration' => $data['duration'] ?? $metadata['duration'] ?? '',
            'thumbnail_url' => $data['thumbnail_url'] ?? $metadata['thumbnail_url'] ?? '',
            'description' => $data['description'] ?? $metadata['description'] ?? '',
            'tags' => json_encode($data['tags'] ?? []),
            'status' => $data['status'] ?? 'to_watch',
            'priority' => $data['priority'] ?? 'medium',
            'personal_notes' => $data['personal_notes'] ?? '',
            'added_from' => $data['added_from'] ?? 'manual',
            'metadata' => json_encode(array_merge($metadata, $data['metadata'] ?? [])),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->create($linkData);
    }

    /**
     * Get user's YouTube links with optional filters
     */
    public function getUserLinks($userId, $filters = []) {
        $sql = "SELECT yl.*, ylc.name as collection_name, ylc.color as collection_color, ylc.icon as collection_icon
                FROM {$this->table} yl
                LEFT JOIN youtube_link_collections ylc ON yl.collection_id = ylc.id
                WHERE yl.user_id = ?";
        
        $params = [$userId];

        // Apply filters
        if (!empty($filters['collection_id'])) {
            $sql .= " AND yl.collection_id = ?";
            $params[] = $filters['collection_id'];
        }

        if (!empty($filters['status'])) {
            $sql .= " AND yl.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['priority'])) {
            $sql .= " AND yl.priority = ?";
            $params[] = $filters['priority'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (yl.title LIKE ? OR yl.channel_name LIKE ? OR yl.description LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $sql .= " ORDER BY yl.priority DESC, yl.created_at DESC";

        if (!empty($filters['limit'])) {
            $sql .= " LIMIT " . intval($filters['limit']);
        }

        $links = $this->db->fetchAll($sql, $params);

        // Decode JSON fields
        if ($links) {
            foreach ($links as &$link) {
                $link['tags'] = json_decode($link['tags'], true) ?? [];
                $link['metadata'] = json_decode($link['metadata'], true) ?? [];
            }
        }

        return $links ?: [];
    }

    /**
     * Get recent links for dashboard
     */
    public function getRecentLinks($userId, $limit = 5) {
        return $this->getUserLinks($userId, ['limit' => $limit]);
    }

    /**
     * Get links by status
     */
    public function getLinksByStatus($userId, $status) {
        return $this->getUserLinks($userId, ['status' => $status]);
    }

    /**
     * Update link status and progress
     */
    public function updateProgress($linkId, $userId, $status, $watchProgress = null) {
        $updateData = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($watchProgress !== null) {
            $updateData['watch_progress'] = $watchProgress;
        }

        // Log analytics
        $this->logAnalytics($userId, $linkId, 'viewed', [
            'status' => $status,
            'progress' => $watchProgress
        ]);

        return $this->update($linkId, $updateData);
    }

    /**
     * Extract YouTube ID from URL
     */
    private function extractYouTubeId($url) {
        $patterns = [
            '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/',
            '/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * Extract metadata from YouTube URL
     */
    private function extractMetadata($url) {
        $metadata = [
            'domain' => parse_url($url, PHP_URL_HOST),
            'extracted_at' => date('Y-m-d H:i:s')
        ];

        // Try to extract basic info from URL
        $youtubeId = $this->extractYouTubeId($url);
        if ($youtubeId) {
            $metadata['youtube_id'] = $youtubeId;
            $metadata['thumbnail_url'] = "https://img.youtube.com/vi/{$youtubeId}/maxresdefault.jpg";
        }

        return $metadata;
    }

    /**
     * Log analytics action
     */
    private function logAnalytics($userId, $linkId, $actionType, $actionData = []) {
        try {
            $sql = "INSERT INTO youtube_link_analytics (user_id, link_id, action_type, action_data, created_at) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $this->db->execute($sql, [
                $userId,
                $linkId,
                $actionType,
                json_encode($actionData),
                date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Log error but don't fail the main operation
            error_log("Failed to log YouTube link analytics: " . $e->getMessage());
        }
    }

    /**
     * Get user statistics
     */
    public function getUserStats($userId) {
        $sql = "SELECT
                    COUNT(*) as total_links,
                    SUM(CASE WHEN status = 'to_watch' THEN 1 ELSE 0 END) as to_watch,
                    SUM(CASE WHEN status = 'watching' THEN 1 ELSE 0 END) as watching,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority
                FROM {$this->table}
                WHERE user_id = ?";

        $stats = $this->db->fetchOne($sql, [$userId]);

        if (!$stats) {
            return [
                'total_links' => 0,
                'to_watch' => 0,
                'watching' => 0,
                'completed' => 0,
                'high_priority' => 0
            ];
        }

        return [
            'total_links' => intval($stats['total_links'] ?? 0),
            'to_watch' => intval($stats['to_watch'] ?? 0),
            'watching' => intval($stats['watching'] ?? 0),
            'completed' => intval($stats['completed'] ?? 0),
            'high_priority' => intval($stats['high_priority'] ?? 0)
        ];
    }

    /**
     * Search links
     */
    public function searchLinks($userId, $query, $limit = 20) {
        return $this->getUserLinks($userId, [
            'search' => $query,
            'limit' => $limit
        ]);
    }

    /**
     * Bulk update links
     */
    public function bulkUpdate($linkIds, $userId, $updateData) {
        if (empty($linkIds)) {
            return false;
        }

        $placeholders = str_repeat('?,', count($linkIds) - 1) . '?';
        $sql = "UPDATE {$this->table} SET ";
        
        $setParts = [];
        $params = [];
        
        foreach ($updateData as $field => $value) {
            $setParts[] = "{$field} = ?";
            $params[] = $value;
        }
        
        $setParts[] = "updated_at = ?";
        $params[] = date('Y-m-d H:i:s');
        
        $sql .= implode(', ', $setParts);
        $sql .= " WHERE id IN ({$placeholders}) AND user_id = ?";
        
        $params = array_merge($params, $linkIds, [$userId]);
        
        return $this->db->execute($sql, $params);
    }
}
