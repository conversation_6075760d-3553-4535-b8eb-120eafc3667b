<?php
/**
 * Tools Dashboard
 */
?>

<link rel="stylesheet" href="/momentum/public/css/tools.css">

<div class="tools-container">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Tools Dashboard</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Access all your productivity tools in one place</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="openQuickCapture()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                <i class="fas fa-camera mr-2"></i>
                Quick Capture
            </button>
        </div>
    </div>

    <!-- AI Assistant Tools Section -->
    <div class="mb-8">
        <div class="flex items-center mb-6">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-brain text-purple-600 dark:text-purple-400"></i>
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">AI Assistant Tools</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Intelligent productivity tools powered by AI</p>
                </div>
            </div>
            <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                New Feature
            </span>
        </div>

        <!-- AI Tools Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
            <!-- AI Prompts -->
            <a href="/momentum/tools/ai-prompts" class="tool-card group">
                <div class="tool-card-icon bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 group-hover:bg-purple-200 dark:group-hover:bg-purple-800">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 class="tool-card-title">AI Prompts</h3>
                <p class="tool-card-description">Manage and execute AI prompts for various tasks</p>
                <div class="tool-card-stats">
                    <span class="stat-item">
                        <i class="fas fa-file-text text-purple-500"></i>
                        <?= $aiAssistantData['promptStats']['total_prompts'] ?? 0 ?> prompts
                    </span>
                </div>
            </a>

            <!-- Quick Capture -->
            <a href="/momentum/tools/quick-capture" class="tool-card group">
                <div class="tool-card-icon bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 group-hover:bg-blue-200 dark:group-hover:bg-blue-800">
                    <i class="fas fa-camera"></i>
                </div>
                <h3 class="tool-card-title">Quick Capture</h3>
                <p class="tool-card-description">Capture screenshots, notes, and voice recordings</p>
                <div class="tool-card-stats">
                    <span class="stat-item">
                        <i class="fas fa-images text-blue-500"></i>
                        <?= $aiAssistantData['captureStats']['total_captures'] ?? 0 ?> captures
                    </span>
                </div>
            </a>

            <!-- Screenshot Tool -->
            <button onclick="openScreenshot()" class="tool-card group text-left">
                <div class="tool-card-icon bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 group-hover:bg-green-200 dark:group-hover:bg-green-800">
                    <i class="fas fa-camera-retro"></i>
                </div>
                <h3 class="tool-card-title">Screenshot</h3>
                <p class="tool-card-description">Take and annotate screenshots instantly</p>
                <div class="tool-card-stats">
                    <span class="stat-item">
                        <i class="fas fa-bolt text-green-500"></i>
                        Instant capture
                    </span>
                </div>
            </button>

            <!-- Quick Note -->
            <a href="/momentum/tools/note" class="tool-card group">
                <div class="tool-card-icon bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 group-hover:bg-orange-200 dark:group-hover:bg-orange-800">
                    <i class="fas fa-sticky-note"></i>
                </div>
                <h3 class="tool-card-title">Quick Note</h3>
                <p class="tool-card-description">Create text notes with tags and categories</p>
                <div class="tool-card-stats">
                    <span class="stat-item">
                        <i class="fas fa-edit text-orange-500"></i>
                        Text notes
                    </span>
                </div>
            </a>

            <!-- Gallery -->
            <a href="/momentum/tools/gallery" class="tool-card group">
                <div class="tool-card-icon bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 group-hover:bg-indigo-200 dark:group-hover:bg-indigo-800">
                    <i class="fas fa-images"></i>
                </div>
                <h3 class="tool-card-title">Gallery</h3>
                <p class="tool-card-description">Browse and manage all your captures</p>
                <div class="tool-card-stats">
                    <span class="stat-item">
                        <i class="fas fa-folder text-indigo-500"></i>
                        View all
                    </span>
                </div>
            </a>
        </div>

        <!-- YouTube Links Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
            <!-- YouTube Links -->
            <a href="/momentum/tools/youtube-links" class="tool-card group">
                <div class="tool-card-icon bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 group-hover:bg-red-200 dark:group-hover:bg-red-800">
                    <i class="fab fa-youtube"></i>
                </div>
                <h3 class="tool-card-title">YouTube Links</h3>
                <p class="tool-card-description">Organize and manage your YouTube video collection</p>
                <div class="tool-card-stats">
                    <span class="stat-item">
                        <i class="fas fa-link text-red-500"></i>
                        <?= $youtubeLinkData['stats']['total_links'] ?? 0 ?> links
                    </span>
                </div>
            </a>
        </div>

        <!-- AI Assistant Stats -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">AI Assistant Overview</h3>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div class="stat-card">
                    <div class="stat-card-icon bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="stat-card-value"><?= $aiAssistantData['promptStats']['total_prompts'] ?? 0 ?></div>
                    <div class="stat-card-label">AI Prompts</div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-icon bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="stat-card-value"><?= $aiAssistantData['captureStats']['total_captures'] ?? 0 ?></div>
                    <div class="stat-card-label">Captures</div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-icon bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-card-value"><?= count($aiAssistantData['recentPrompts'] ?? []) ?></div>
                    <div class="stat-card-label">Recent Activity</div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-icon bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-card-value"><?= count($aiAssistantData['recentCaptures'] ?? []) ?></div>
                    <div class="stat-card-label">Recent Captures</div>
                </div>
            </div>

            <!-- Recent Activity -->
            <?php if (!empty($aiAssistantData['recentPrompts']) || !empty($aiAssistantData['recentCaptures'])): ?>
                <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Recent Activity</h4>
                    <div class="space-y-2">
                        <?php
                        $allActivity = [];

                        // Add recent prompts
                        foreach ($aiAssistantData['recentPrompts'] as $prompt) {
                            $allActivity[] = [
                                'type' => 'prompt',
                                'title' => $prompt['title'],
                                'time' => $prompt['created_at'],
                                'icon' => 'fa-brain',
                                'color' => 'purple',
                                'url' => '/momentum/ai-prompts/view/' . $prompt['id']
                            ];
                        }

                        // Add recent captures
                        foreach ($aiAssistantData['recentCaptures'] as $capture) {
                            $allActivity[] = [
                                'type' => 'capture',
                                'title' => $capture['title'] ?: ucfirst($capture['type']) . ' capture',
                                'time' => $capture['created_at'],
                                'icon' => $capture['type'] === 'screenshot' ? 'fa-camera' : ($capture['type'] === 'note' ? 'fa-sticky-note' : 'fa-microphone'),
                                'color' => $capture['type'] === 'screenshot' ? 'blue' : ($capture['type'] === 'note' ? 'green' : 'orange'),
                                'url' => '/momentum/quick-capture/view/' . $capture['id']
                            ];
                        }

                        // Sort by time (most recent first)
                        usort($allActivity, function($a, $b) {
                            return strtotime($b['time']) - strtotime($a['time']);
                        });

                        // Show only the 5 most recent
                        $allActivity = array_slice($allActivity, 0, 5);
                        ?>

                        <?php foreach ($allActivity as $activity): ?>
                            <a href="<?= $activity['url'] ?>" class="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
                                <div class="w-8 h-8 bg-<?= $activity['color'] ?>-100 dark:bg-<?= $activity['color'] ?>-900 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas <?= $activity['icon'] ?> text-<?= $activity['color'] ?>-600 dark:text-<?= $activity['color'] ?>-400 text-sm"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate"><?= htmlspecialchars($activity['title']) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400"><?= date('M j, g:i A', strtotime($activity['time'])) ?></p>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Other Tools Section -->
    <div class="mb-8">
        <div class="flex items-center mb-6">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-tools text-gray-600 dark:text-gray-400"></i>
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Utility Tools</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Essential productivity utilities</p>
                </div>
            </div>
        </div>

        <!-- Utility Tools Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Currency Converter -->
            <a href="/momentum/tools/currency-converter" class="tool-card group">
                <div class="tool-card-icon bg-emerald-100 dark:bg-emerald-900 text-emerald-600 dark:text-emerald-400 group-hover:bg-emerald-200 dark:group-hover:bg-emerald-800">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <h3 class="tool-card-title">Currency Converter</h3>
                <p class="tool-card-description">Convert between different currencies with live rates</p>
                <div class="tool-card-stats">
                    <span class="stat-item">
                        <i class="fas fa-globe text-emerald-500"></i>
                        Live rates
                    </span>
                </div>
            </a>

            <!-- Astrology Tools -->
            <a href="/momentum/astrology" class="tool-card group">
                <div class="tool-card-icon bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 group-hover:bg-purple-200 dark:group-hover:bg-purple-800">
                    <i class="fas fa-star"></i>
                </div>
                <h3 class="tool-card-title">Astrology</h3>
                <p class="tool-card-description">Rahu Kalaya timings and Vedic astrology guidance</p>
                <div class="tool-card-stats">
                    <span class="stat-item">
                        <i class="fas fa-clock text-purple-500"></i>
                        Daily timings
                    </span>
                </div>
            </a>

            <!-- More tools can be added here -->
            <div class="tool-card-placeholder">
                <div class="tool-card-icon bg-gray-100 dark:bg-gray-700 text-gray-400">
                    <i class="fas fa-plus"></i>
                </div>
                <h3 class="tool-card-title text-gray-500 dark:text-gray-400">More Tools Coming</h3>
                <p class="tool-card-description text-gray-400 dark:text-gray-500">Additional productivity tools will be added here</p>
            </div>

            <div class="tool-card-placeholder">
                <div class="tool-card-icon bg-gray-100 dark:bg-gray-700 text-gray-400">
                    <i class="fas fa-plus"></i>
                </div>
                <h3 class="tool-card-title text-gray-500 dark:text-gray-400">Suggest a Tool</h3>
                <p class="tool-card-description text-gray-400 dark:text-gray-500">Have an idea for a new tool? Let us know!</p>
            </div>
        </div>
    </div>

    <!-- Keyboard Shortcuts -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            <i class="fas fa-keyboard mr-2"></i>
            Keyboard Shortcuts
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="flex items-center">
                <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-xs mr-2">Alt+Q</kbd>
                <span class="text-sm text-gray-700 dark:text-gray-300">Quick Capture</span>
            </div>
            <div class="flex items-center">
                <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-xs mr-2">Alt+S</kbd>
                <span class="text-sm text-gray-700 dark:text-gray-300">Screenshot</span>
            </div>
            <div class="flex items-center">
                <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-xs mr-2">Alt+N</kbd>
                <span class="text-sm text-gray-700 dark:text-gray-300">Quick Note</span>
            </div>
            <div class="flex items-center">
                <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-xs mr-2">Alt+I</kbd>
                <span class="text-sm text-gray-700 dark:text-gray-300">AI Prompts</span>
            </div>
            <div class="flex items-center">
                <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-xs mr-2">Alt+Y</kbd>
                <span class="text-sm text-gray-700 dark:text-gray-300">YouTube Links</span>
            </div>
        </div>
    </div>
</div>

<!-- Quick Capture Modal -->
<div id="quickCaptureModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Capture</h3>
                <button onclick="closeQuickCapture()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="space-y-3">
                <button onclick="openScreenshot()" class="w-full flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 rounded-lg border border-blue-200 dark:border-blue-700 transition-colors duration-200">
                    <i class="fas fa-camera text-blue-600 dark:text-blue-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-blue-700 dark:text-blue-300">Take Screenshot</p>
                        <p class="text-xs text-blue-600 dark:text-blue-400">Capture and annotate screen content</p>
                    </div>
                </button>

                <a href="/momentum/tools/note" class="w-full flex items-center p-3 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/40 rounded-lg border border-green-200 dark:border-green-700 transition-colors duration-200">
                    <i class="fas fa-sticky-note text-green-600 dark:text-green-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-green-700 dark:text-green-300">Quick Note</p>
                        <p class="text-xs text-green-600 dark:text-green-400">Create a text note instantly</p>
                    </div>
                </a>

                <a href="/momentum/tools/voice" class="w-full flex items-center p-3 bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/40 rounded-lg border border-orange-200 dark:border-orange-700 transition-colors duration-200">
                    <i class="fas fa-microphone text-orange-600 dark:text-orange-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-orange-700 dark:text-orange-300">Voice Note</p>
                        <p class="text-xs text-orange-600 dark:text-orange-400">Record audio with transcription</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Quick Capture Modal Functions
function openQuickCapture() {
    document.getElementById('quickCaptureModal').classList.remove('hidden');
}

function closeQuickCapture() {
    document.getElementById('quickCaptureModal').classList.add('hidden');
}

function openScreenshot() {
    closeQuickCapture();
    window.open('/momentum/tools/screenshot', '_blank', 'width=1200,height=800');
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.altKey && !e.ctrlKey && !e.shiftKey && !e.metaKey) {
        const activeElement = document.activeElement;
        const isInputFocused = activeElement.tagName === 'INPUT' ||
                             activeElement.tagName === 'TEXTAREA' ||
                             activeElement.contentEditable === 'true';

        if (!isInputFocused) {
            switch(e.key.toLowerCase()) {
                case 'q':
                    e.preventDefault();
                    openQuickCapture();
                    break;
                case 's':
                    e.preventDefault();
                    openScreenshot();
                    break;
                case 'n':
                    e.preventDefault();
                    window.location.href = '/momentum/tools/note';
                    break;
                case 'i':
                    e.preventDefault();
                    window.location.href = '/momentum/tools/ai-prompts';
                    break;
                case 'y':
                    e.preventDefault();
                    window.location.href = '/momentum/tools/youtube-links';
                    break;
            }
        }
    }
});

// Close modal when clicking outside
document.getElementById('quickCaptureModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQuickCapture();
    }
});
</script>
