<?php
/**
 * Add YouTube Link Page
 */

$title = 'Add YouTube Link';
$stylesheets = ['/momentum/css/youtube-links.css'];
$scripts = ['/momentum/js/youtube-links.js'];

ob_start();
?>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <a href="/momentum/tools/youtube-links" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-4">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Add YouTube Link</h1>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Add a new video to your collection</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <form method="POST" action="/momentum/tools/youtube-links/add" class="p-6">
                <div class="space-y-6">
                    <!-- URL Field -->
                    <div>
                        <label for="url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            YouTube URL *
                        </label>
                        <input type="url" 
                               id="url" 
                               name="url" 
                               required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white"
                               placeholder="https://www.youtube.com/watch?v=..."
                               value="<?= htmlspecialchars($_POST['url'] ?? '') ?>">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Paste the full YouTube video URL here
                        </p>
                    </div>

                    <!-- Title Field -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Custom Title (Optional)
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white"
                               placeholder="Leave empty to auto-extract from YouTube"
                               value="<?= htmlspecialchars($_POST['title'] ?? '') ?>">
                    </div>

                    <!-- Collection Field -->
                    <div>
                        <label for="collection_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Collection
                        </label>
                        <select id="collection_id" 
                                name="collection_id"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white">
                            <?php foreach ($collections as $collection): ?>
                                <option value="<?= $collection['id'] ?>" 
                                        <?= ($collection['is_default'] || ($_POST['collection_id'] ?? '') == $collection['id']) ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($collection['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Priority Field -->
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Priority
                        </label>
                        <select id="priority" 
                                name="priority"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white">
                            <option value="low" <?= ($_POST['priority'] ?? '') === 'low' ? 'selected' : '' ?>>Low</option>
                            <option value="medium" <?= ($_POST['priority'] ?? 'medium') === 'medium' ? 'selected' : '' ?>>Medium</option>
                            <option value="high" <?= ($_POST['priority'] ?? '') === 'high' ? 'selected' : '' ?>>High</option>
                        </select>
                    </div>

                    <!-- Tags Field -->
                    <div>
                        <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Tags (Optional)
                        </label>
                        <input type="text" 
                               id="tags" 
                               name="tags"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white"
                               placeholder="tutorial, programming, ai (comma-separated)"
                               value="<?= htmlspecialchars($_POST['tags'] ?? '') ?>">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Separate multiple tags with commas
                        </p>
                    </div>

                    <!-- Personal Notes Field -->
                    <div>
                        <label for="personal_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Personal Notes (Optional)
                        </label>
                        <textarea id="personal_notes" 
                                  name="personal_notes"
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white"
                                  placeholder="Add your personal notes about this video..."><?= htmlspecialchars($_POST['personal_notes'] ?? '') ?></textarea>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="/momentum/tools/youtube-links" 
                       class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Add Link
                    </button>
                </div>
            </form>
        </div>

        <!-- Tips -->
        <div class="mt-6 bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
                <i class="fas fa-lightbulb mr-2"></i>
                Tips
            </h3>
            <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• The system will automatically extract video title, thumbnail, and duration from YouTube</li>
                <li>• Use collections to organize your videos by topic or purpose</li>
                <li>• High priority videos will be highlighted in your dashboard</li>
                <li>• Tags help you find videos later using search</li>
            </ul>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include dirname(__DIR__, 2) . '/layouts/default.php';
?>
