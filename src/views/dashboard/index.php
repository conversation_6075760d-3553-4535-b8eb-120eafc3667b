<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Dashboard Header with Simplified Controls -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    <?= date('l, F j, Y') ?> • <span id="view-mode-text" class="text-primary-600 dark:text-primary-400">ADHD Optimized View</span>
                </p>
            </div>

            <div class="flex flex-wrap items-center gap-3">
                <!-- Quick Action Buttons - Most Important Actions -->
                <a href="/momentum/tasks/create" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1.5"></i> New Task
                </a>
                <a href="/momentum/ideas/create" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                    <i class="fas fa-lightbulb mr-1.5"></i> New Idea
                </a>

                <!-- Layout Selector (Fixed Version) -->
                <div class="relative inline-block text-left">
                    <button id="layout-selector-button" type="button" aria-expanded="false" aria-haspopup="true"
                        class="layout-button inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-columns mr-1.5"></i> Layout <i class="fas fa-chevron-down ml-1.5 text-xs"></i>
                    </button>
                </div>

                <!-- Layout Dropdown (Separate from button for better positioning) -->
                <div id="layout-dropdown" class="hidden fixed origin-top-right mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 dark:divide-gray-700 focus:outline-none z-50">
                    <div class="py-1">
                        <a href="#" data-view="adhd-optimized" class="view-option group flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 bg-primary-50 dark:bg-primary-900 border-l-4 border-primary-500">
                            <i class="fas fa-brain mr-3 text-primary-500"></i> ADHD Optimized
                        </a>
                        <a href="#" data-view="focus" class="view-option group flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-bullseye mr-3 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"></i> Focus View
                        </a>
                        <a href="#" data-view="standard" class="view-option group flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-th-large mr-3 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"></i> Standard View
                        </a>
                        <a href="#" data-view="custom" class="view-option group flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-sliders-h mr-3 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"></i> Custom View
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Widget Controls (Only visible in Custom layout) -->
        <div id="widget-controls" class="hidden mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Widget Controls</h3>
            <div class="flex flex-wrap gap-2 mb-3">
                <button id="save-layout-btn" class="px-3 py-1.5 text-sm bg-purple-600 hover:bg-purple-700 text-white rounded transition-colors">
                    <i class="fas fa-save mr-1"></i> Save Layout
                </button>
                <button id="reset-layout-btn" class="px-3 py-1.5 text-sm bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded transition-colors">
                    <i class="fas fa-undo mr-1"></i> Reset Layout
                </button>
            </div>
            <div class="flex items-center gap-2 mb-2">
                <i class="fas fa-info-circle text-purple-500"></i>
                <span class="text-sm text-gray-700 dark:text-gray-300">Drag and drop widgets to rearrange them</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400 bg-purple-50 dark:bg-purple-900/20 p-2 rounded">
                <p><i class="fas fa-lightbulb text-yellow-500 mr-1"></i> Tip: Arrange widgets based on your workflow priorities</p>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div id="dashboard-widgets" class="space-y-6" data-view-mode="adhd-optimized" data-arrangement="adhd-optimized">
            <!-- Section 1: Current Focus (Full Width) -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg rounded-lg border-l-4 border-primary-500" id="current-focus-widget" data-widget="current-focus-widget">
                <div class="px-5 py-5">
                    <div class="flex justify-between items-center mb-3">
                        <div class="flex items-center">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-bullseye text-primary-500 mr-2.5"></i> Current Focus
                            </h2>
                        </div>
                        <div class="flex space-x-2">
                            <button id="focus-mode-toggle" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-expand mr-1.5"></i> Enter Focus Mode
                            </button>
                            <?php if ($currentFocusTask): ?>
                                <button id="clear-focus-task" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors duration-200">
                                    <i class="fas fa-times mr-1.5"></i> Clear Focus
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if ($currentFocusTask): ?>
                        <div id="current-focus-content" class="bg-primary-50 dark:bg-primary-900/30 rounded-lg border border-primary-100 dark:border-primary-800">
                            <div class="p-4">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                    <div class="mb-4 md:mb-0 md:pr-4">
                                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2"><?= View::escape($currentFocusTask['title']) ?></h3>
                                        <?php if (!empty($currentFocusTask['description'])): ?>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mb-3"><?= nl2br(View::escape($currentFocusTask['description'])) ?></p>
                                        <?php endif; ?>

                                        <div class="flex flex-wrap items-center gap-2 mb-1">
                                            <!-- Task metadata badges -->
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php if ($currentFocusTask['status'] === 'todo'): ?>
                                                    bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                                <?php elseif ($currentFocusTask['status'] === 'in_progress'): ?>
                                                    bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                                <?php elseif ($currentFocusTask['status'] === 'done'): ?>
                                                    bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                                <?php endif; ?>
                                            ">
                                                <?php if ($currentFocusTask['status'] === 'todo'): ?>
                                                    <i class="fas fa-circle text-xs mr-1"></i> To Do
                                                <?php elseif ($currentFocusTask['status'] === 'in_progress'): ?>
                                                    <i class="fas fa-spinner text-xs mr-1"></i> In Progress
                                                <?php elseif ($currentFocusTask['status'] === 'done'): ?>
                                                    <i class="fas fa-check text-xs mr-1"></i> Done
                                                <?php endif; ?>
                                            </span>

                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php if ($currentFocusTask['priority'] === 'low'): ?>
                                                    bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                                <?php elseif ($currentFocusTask['priority'] === 'medium'): ?>
                                                    bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                                <?php elseif ($currentFocusTask['priority'] === 'high'): ?>
                                                    bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                                <?php elseif ($currentFocusTask['priority'] === 'urgent'): ?>
                                                    bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                                <?php endif; ?>
                                            ">
                                                <?php if ($currentFocusTask['priority'] === 'urgent'): ?>
                                                    <i class="fas fa-exclamation-circle text-xs mr-1"></i>
                                                <?php elseif ($currentFocusTask['priority'] === 'high'): ?>
                                                    <i class="fas fa-arrow-up text-xs mr-1"></i>
                                                <?php endif; ?>
                                                <?= ucfirst($currentFocusTask['priority']) ?> Priority
                                            </span>

                                            <?php if (!empty($currentFocusTask['category_id']) && !empty($currentFocusTask['category_color'])): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: <?= $currentFocusTask['category_color'] ?>25; color: <?= $currentFocusTask['category_color'] ?>;">
                                                    <span class="w-1.5 h-1.5 rounded-full mr-1" style="background-color: <?= $currentFocusTask['category_color'] ?>;"></span>
                                                    <?= View::escape($currentFocusTask['category_name'] ?? 'Category') ?>
                                                </span>
                                            <?php endif; ?>

                                            <?php if ($currentFocusTask['due_date']): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                    <i class="far fa-calendar text-xs mr-1"></i> Due: <?= View::formatDate($currentFocusTask['due_date']) ?>
                                                    <?php if ($currentFocusTask['due_time']): ?>
                                                        at <?= View::formatTime($currentFocusTask['due_time']) ?>
                                                    <?php endif; ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="flex space-x-3">
                                        <a href="/momentum/productivity/focus-timer?task_id=<?= $currentFocusTask['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                            <i class="fas fa-clock mr-1.5"></i> Start Timer
                                        </a>

                                        <a href="/momentum/tasks/complete/<?= $currentFocusTask['id'] ?>" id="mark-complete-btn" data-task-id="<?= $currentFocusTask['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                                            <i class="fas fa-check mr-1.5"></i> Complete
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div id="current-focus-empty" class="bg-gray-50 dark:bg-gray-700 p-5 rounded-lg text-center">
                            <div class="flex flex-col items-center justify-center py-4">
                                <i class="fas fa-bullseye text-primary-400 dark:text-primary-300 text-4xl mb-3"></i>
                                <p class="text-gray-600 dark:text-gray-300 mb-2 text-lg">No task currently in focus</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto mb-4">Select a task from "Today's Tasks" below to focus on it.</p>
                                <a href="/momentum/tasks" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors duration-200">
                                    <i class="fas fa-tasks mr-1.5"></i> View All Tasks
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Section 2: Task Management (2-column grid) -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Today's Tasks Widget -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="today-tasks" style="height: 400px; max-height: 400px; overflow: hidden; display: flex; flex-direction: column;">
                    <div class="px-5 py-5" style="display: flex; flex-direction: column; height: 100%; overflow: hidden;">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-calendar-day text-blue-500 mr-2"></i> Today's Tasks
                            </h2>
                            <a href="/momentum/tasks?due_date=<?= date('Y-m-d') ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                View all <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>

                        <!-- Today's tasks content -->
                        <div class="task-list-container" id="today-tasks-container" style="flex: 1; display: flex; flex-direction: column; overflow: hidden; position: relative;">
                            <?php if (empty($todayTasks)): ?>
                                <div class="task-list-empty">
                                    <p class="text-gray-500 dark:text-gray-400">No tasks for today</p>
                                    <a href="/momentum/tasks/create" class="mt-2 inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                        <i class="fas fa-plus mr-1"></i> Add a task
                                    </a>
                                </div>
                            <?php else: ?>
                                <ul class="divide-y divide-gray-200 dark:divide-gray-700" id="today-tasks-list" style="max-height: 300px; overflow-y: auto; padding-right: 5px; margin-right: -5px;">
                                    <?php foreach ($todayTasks as $task): ?>
                                        <li class="py-3 task-item <?= $currentFocusTask && $currentFocusTask['id'] == $task['id'] ? 'bg-primary-50 dark:bg-primary-900 border-l-4 border-primary-500 pl-2' : '' ?>" data-task-id="<?= $task['id'] ?>">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center flex-1">
                                                    <input type="checkbox" data-task-id="<?= $task['id'] ?>" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $task['status'] === 'done' ? 'checked' : '' ?>>
                                                    <div class="ml-3 flex-1">
                                                        <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-sm font-medium text-gray-900 dark:text-white <?= $task['status'] === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : '' ?>">
                                                            <?= View::escape($task['title']) ?>
                                                        </a>
                                                        <div class="flex flex-wrap items-center mt-1">
                                                            <?php if ($task['due_time']): ?>
                                                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">
                                                                    <i class="far fa-clock"></i> <?= View::formatTime($task['due_time']) ?>
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if (!empty($task['estimated_time'])): ?>
                                                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">
                                                                    <i class="fas fa-hourglass-half"></i> <?= $task['estimated_time'] ?> min
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if (!empty($task['category_id']) && !empty($task['category_color'])): ?>
                                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium mr-2" style="background-color: <?= $task['category_color'] ?>25; color: <?= $task['category_color'] ?>;">
                                                                    <?= View::escape($task['category_name'] ?? 'Category') ?>
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if ($task['priority'] === 'high'): ?>
                                                                <span class="text-orange-500 dark:text-orange-400 text-xs" title="High Priority">
                                                                    <i class="fas fa-arrow-up"></i> High
                                                                </span>
                                                            <?php elseif ($task['priority'] === 'urgent'): ?>
                                                                <span class="text-red-500 dark:text-red-400 text-xs" title="Urgent">
                                                                    <i class="fas fa-exclamation-circle"></i> Urgent
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="ml-2 flex items-center">
                                                    <?php if ($currentFocusTask && $currentFocusTask['id'] == $task['id']): ?>
                                                        <span class="text-xs text-primary-600 dark:text-primary-400 font-medium mr-2">
                                                            <i class="fas fa-bullseye"></i> In Focus
                                                        </span>
                                                    <?php else: ?>
                                                        <a href="/momentum/tasks/set-focus/<?= $task['id'] ?>" class="inline-flex items-center px-2 py-1 border border-transparent rounded text-xs font-medium text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-800 hover:bg-primary-200 dark:hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                                            <i class="fas fa-bullseye mr-1"></i> Focus
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>

                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Overdue Tasks Widget -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="overdue-tasks" style="height: 400px; max-height: 400px; overflow: hidden; display: flex; flex-direction: column;">
                    <div class="px-5 py-5" style="display: flex; flex-direction: column; height: 100%; overflow: hidden;">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-exclamation-circle text-red-500 mr-2"></i> Overdue Tasks
                            </h2>
                            <a href="/momentum/tasks?filter=overdue" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                View all <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>

                        <!-- Overdue tasks content -->
                        <div class="task-list-container" id="overdue-tasks-container" style="flex: 1; display: flex; flex-direction: column; overflow: hidden; position: relative;">
                            <?php if (empty($overdueTasks)): ?>
                                <div class="task-list-empty">
                                    <p class="text-gray-500 dark:text-gray-400">No overdue tasks</p>
                                </div>
                            <?php else: ?>
                                <ul class="divide-y divide-gray-200 dark:divide-gray-700" id="overdue-tasks-list" style="max-height: 300px; overflow-y: auto; padding-right: 5px; margin-right: -5px;">
                                    <?php foreach ($overdueTasks as $task): ?>
                                        <li class="py-3 task-item <?= $currentFocusTask && $currentFocusTask['id'] == $task['id'] ? 'bg-primary-50 dark:bg-primary-900 border-l-4 border-primary-500 pl-2' : '' ?>" data-task-id="<?= $task['id'] ?>">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center flex-1">
                                                    <input type="checkbox" data-task-id="<?= $task['id'] ?>" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $task['status'] === 'done' ? 'checked' : '' ?>>
                                                    <div class="ml-3 flex-1">
                                                        <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-sm font-medium text-gray-900 dark:text-white <?= $task['status'] === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : '' ?>">
                                                            <?= View::escape($task['title']) ?>
                                                        </a>
                                                        <div class="flex flex-wrap items-center mt-1">
                                                            <span class="text-xs text-red-500 dark:text-red-400 mr-2">
                                                                <i class="far fa-calendar"></i> Due: <?= View::formatDate($task['due_date']) ?>
                                                                <?php if ($task['due_time']): ?>
                                                                    at <?= View::formatTime($task['due_time']) ?>
                                                                <?php endif; ?>
                                                            </span>
                                                            <?php if (!empty($task['category_id']) && !empty($task['category_color'])): ?>
                                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium mr-2" style="background-color: <?= $task['category_color'] ?>25; color: <?= $task['category_color'] ?>;">
                                                                    <?= View::escape($task['category_name'] ?? 'Category') ?>
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if ($task['priority'] === 'high'): ?>
                                                                <span class="text-orange-500 dark:text-orange-400 text-xs" title="High Priority">
                                                                    <i class="fas fa-arrow-up"></i> High
                                                                </span>
                                                            <?php elseif ($task['priority'] === 'urgent'): ?>
                                                                <span class="text-red-500 dark:text-red-400 text-xs" title="Urgent">
                                                                    <i class="fas fa-exclamation-circle"></i> Urgent
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="ml-2 flex items-center">
                                                    <?php if ($currentFocusTask && $currentFocusTask['id'] == $task['id']): ?>
                                                        <span class="text-xs text-primary-600 dark:text-primary-400 font-medium mr-2">
                                                            <i class="fas fa-bullseye"></i> In Focus
                                                        </span>
                                                    <?php else: ?>
                                                        <a href="/momentum/tasks/set-focus/<?= $task['id'] ?>" class="inline-flex items-center px-2 py-1 border border-transparent rounded text-xs font-medium text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-800 hover:bg-primary-200 dark:hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                                            <i class="fas fa-bullseye mr-1"></i> Focus
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>

                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 2.3: AI Agents Army -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg rounded-lg border-l-4 border-indigo-500" data-widget="ai-agents-widget">
                <?php include __DIR__ . '/widgets/ai_agents_widget.php'; ?>
            </div>

            <!-- Section 2.4: AI Assistant -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg rounded-lg border-l-4 border-purple-500" data-widget="ai-assistant-widget">
                <?php include __DIR__ . '/widgets/ai_assistant_widget.php'; ?>
            </div>

            <!-- Section 2.5: YouTube Links -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-lg rounded-lg border-l-4 border-red-500" data-widget="youtube-links">
                <?php include __DIR__ . '/widgets/youtube_links_widget.php'; ?>
            </div>

            <!-- Section 2.6: Income Opportunities -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="income-opportunities">
                <?php include __DIR__ . '/widgets/income_opportunities.php'; ?>
            </div>

            <!-- Section 2.7: Passive Income Portfolio -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="passive-income">
                <?php include __DIR__ . '/widgets/passive_income.php'; ?>
            </div>

            <!-- Section 2.8: Freelance Project Manager -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="freelance">
                <?php include __DIR__ . '/widgets/freelance.php'; ?>
            </div>

            <!-- Section 2.9: Online Business Dashboard -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="online-business">
                <div class="px-5 py-5">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-chart-line text-cyan-500 mr-2"></i> Online Business Dashboard
                        </h2>
                        <a href="/momentum/online-business" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                            View dashboard <i class="fas fa-chevron-right ml-1 text-xs"></i>
                        </a>
                    </div>

                    <div class="space-y-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400">Monitor key metrics for your online business ventures.</p>

                        <div class="grid grid-cols-2 gap-3">
                            <div class="bg-cyan-50 dark:bg-cyan-900/30 p-3 rounded-lg">
                                <h3 class="text-sm font-medium text-cyan-800 dark:text-cyan-300 mb-1">Business Ventures</h3>
                                <div class="flex items-center">
                                    <i class="fas fa-store text-cyan-500 mr-2"></i>
                                    <a href="/momentum/online-business" class="text-sm text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">Manage your ventures</a>
                                </div>
                            </div>

                            <div class="bg-cyan-50 dark:bg-cyan-900/30 p-3 rounded-lg">
                                <h3 class="text-sm font-medium text-cyan-800 dark:text-cyan-300 mb-1">Performance Metrics</h3>
                                <div class="flex items-center">
                                    <i class="fas fa-chart-bar text-cyan-500 mr-2"></i>
                                    <a href="/momentum/online-business" class="text-sm text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">Track your metrics</a>
                                </div>
                            </div>
                        </div>

                        <a href="/momentum/help/online-business-features" class="block text-xs text-center text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 mt-2">
                            Learn more about this feature
                        </a>
                    </div>
                </div>
            </div>


            <!-- Section 3: Support & Resources (3-column grid) -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Keyboard Shortcuts Guide -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="keyboard-shortcuts">
                    <div class="px-5 py-5">
                        <div class="flex justify-between items-center mb-3">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-keyboard text-purple-500 mr-2"></i> Keyboard Shortcuts
                            </h2>
                            <button id="toggle-shortcuts-details" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                Show Details
                            </button>
                        </div>
                        <div class="grid grid-cols-2 gap-3 mb-3">
                            <div class="bg-gray-50 dark:bg-gray-700 p-2 rounded">
                                <p class="text-xs text-gray-700 dark:text-gray-300 font-medium">Navigation</p>
                                <p class="text-sm text-gray-900 dark:text-white">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Backspace</kbd> Go Back
                                </p>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 p-2 rounded">
                                <p class="text-xs text-gray-700 dark:text-gray-300 font-medium">Feature Access</p>
                                <p class="text-sm text-gray-900 dark:text-white">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Alt</kbd> + Letter
                                </p>
                            </div>
                        </div>
                        <div id="shortcuts-details" class="hidden bg-gray-50 dark:bg-gray-700 p-3 rounded">
                            <h4 class="text-xs font-medium text-gray-900 dark:text-white mb-2">Feature Shortcuts (Alt + Key)</h4>
                            <div class="grid grid-cols-2 gap-2 text-xs">
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">A</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">ADHD Management</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">T</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">Task Management</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">P</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">Project Management</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">F</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">Financial Management</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">Q</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">Quick Capture</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">S</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">Screenshot</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">N</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">Quick Note</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">I</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">AI Prompts</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded mr-1 text-xs">Y</kbd>
                                    <span class="text-gray-700 dark:text-gray-300">YouTube Links</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ADHD Guide Widget -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="adhd-guide">
                    <div class="px-5 py-5">
                        <div class="flex justify-between items-center mb-3">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-brain text-green-500 mr-2"></i> ADHD Guide
                            </h2>
                            <a href="/momentum/adhd/guide" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                View full guide
                            </a>
                        </div>

                        <div class="space-y-3">
                            <div class="bg-green-50 dark:bg-green-900/30 p-3 rounded-lg">
                                <h3 class="text-sm font-medium text-green-800 dark:text-green-300 mb-1">Today's Tip</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Break large tasks into smaller, manageable chunks to reduce overwhelm and make progress easier to track.</p>
                            </div>

                            <?php if ($adhdData && !empty($adhdData['hasLoggedToday'])): ?>
                                <div class="bg-primary-50 dark:bg-primary-900/30 p-3 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-primary-500 mr-2"></i>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">You've logged your symptoms today!</p>
                                    </div>
                                </div>
                            <?php else: ?>
                                <a href="/momentum/adhd/symptom-tracker/log" class="block bg-gray-50 dark:bg-gray-700 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                    <div class="flex items-center">
                                        <i class="fas fa-clipboard-list text-gray-500 dark:text-gray-400 mr-2"></i>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">Log your symptoms today</p>
                                    </div>
                                </a>
                            <?php endif; ?>

                            <?php if ($adhdData && !empty($adhdData['currentStreak'])): ?>
                                <div class="bg-orange-50 dark:bg-orange-900/30 p-3 rounded-lg">
                                    <h3 class="text-sm font-medium text-orange-800 dark:text-orange-300 mb-1">Current Streak</h3>
                                    <div class="flex items-center">
                                        <i class="fas fa-fire text-orange-500 mr-2"></i>
                                        <p class="text-sm text-gray-600 dark:text-gray-300"><?= $adhdData['currentStreak'] ?> days of consistent tracking</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Help Center Widget -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="help-center">
                    <div class="px-5 py-5">
                        <div class="flex justify-between items-center mb-3">
                            <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-question-circle text-amber-500 mr-2"></i> Help Center
                            </h2>
                            <a href="/momentum/help" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                View all resources
                            </a>
                        </div>

                        <div class="grid grid-cols-1 gap-2">
                            <a href="/momentum/help/getting-started" class="block bg-gray-50 dark:bg-gray-700 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Getting Started</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Learn the basics of using Momentum</p>
                            </a>

                            <a href="/momentum/help/adhd-strategies" class="block bg-gray-50 dark:bg-gray-700 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-1">ADHD Strategies</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Effective techniques for ADHD management</p>
                            </a>

                            <a href="/momentum/help/dashboard-redesign-plan" class="block bg-primary-50 dark:bg-primary-900/30 p-3 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-800/50 transition-colors">
                                <h3 class="text-sm font-medium text-primary-900 dark:text-primary-100 mb-1">Dashboard Redesign</h3>
                                <p class="text-xs text-primary-700 dark:text-primary-300">Learn about the new ADHD-friendly dashboard</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Direct Button Fix Script has been replaced by dropdown-fix-consolidated.js -->
<!--
<script>
/**
 * Direct Button Fix
 *
 * This script provides a direct fix for buttons that aren't working on the dashboard.
 * It uses a more direct approach to ensure the buttons work regardless of other scripts.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Direct Button Fix loaded (inline)');

    // Note: All button fixes are now handled by external scripts
    // - Layout button is handled by layout-dropdown-initializer.js
    // - Clear Focus Button is handled by direct-button-fix.js
    // - Focus Mode Toggle is handled by focus-button-handler.js
    // - Task Checkboxes are handled by direct-button-fix.js
    // - Mark Complete Button is handled by direct-button-fix.js
});

    /*
    // The following code is kept for reference only and is not active

    /**
     * Fix Layout Button
     */
    /*
    function fixLayoutButton() {
        const layoutButton = document.getElementById('layout-selector-button');
        const layoutDropdown = document.getElementById('layout-dropdown');

        if (!layoutButton || !layoutDropdown) {
            console.log('Layout button or dropdown not found');
            return;
        }
    */

    /*
        console.log('Fixing layout button');

        // Remove any existing event listeners
        const newButton = layoutButton.cloneNode(true);
        layoutButton.parentNode.replaceChild(newButton, layoutButton);

        // Add direct click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Layout button clicked (direct fix)');

            // Toggle dropdown visibility
            const isHidden = layoutDropdown.classList.contains('hidden');

            if (isHidden) {
                // Show dropdown
                layoutDropdown.classList.remove('hidden');
                layoutDropdown.style.display = 'block';

                // Position dropdown
                const buttonRect = newButton.getBoundingClientRect();
                layoutDropdown.style.position = 'absolute';
                layoutDropdown.style.top = (buttonRect.bottom + window.scrollY) + 'px';
                layoutDropdown.style.left = (buttonRect.left + window.scrollX) + 'px';
                layoutDropdown.style.zIndex = '9999';

                // Set active state
                newButton.setAttribute('aria-expanded', 'true');
            } else {
                // Hide dropdown
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (layoutDropdown && !layoutDropdown.classList.contains('hidden') &&
                !newButton.contains(e.target) && !layoutDropdown.contains(e.target)) {
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
            }
        });

        // Fix view options
        const viewOptions = layoutDropdown.querySelectorAll('[data-view]');
        viewOptions.forEach(option => {
            // Remove any existing event listeners
            const newOption = option.cloneNode(true);
            option.parentNode.replaceChild(newOption, option);

            // Add direct click handler
            newOption.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const viewMode = this.getAttribute('data-view');
                console.log('View option clicked (direct fix):', viewMode);

                // Apply layout
                applyLayout(viewMode);

                // Hide dropdown
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
            });
        });
    }
    */

    /**
     * Apply Layout
     */
    /*
    function applyLayout(layoutName) {
        console.log('Applying layout (direct fix):', layoutName);

        // Get dashboard widgets container
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }

        // Update view mode text
        const viewModeText = document.getElementById('view-mode-text');
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }

        // Update data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);

        // Save preference
        localStorage.setItem('dashboard_view_mode', layoutName);

        // Apply layout-specific class to body
        document.body.classList.remove('layout-adhd-optimized', 'layout-focus', 'layout-standard', 'layout-custom');
        document.body.classList.add('layout-' + layoutName);

        // Show/hide widget controls
        const widgetControls = document.getElementById('widget-controls');
        if (widgetControls) {
            widgetControls.style.display = layoutName === 'custom' ? 'block' : 'none';
        }

        // Apply specific layout styles
        applyLayoutStyles(layoutName);

        // Show success message
        showSuccessMessage(`${layoutName.charAt(0).toUpperCase() + layoutName.slice(1)} layout applied`);
    }
    */

    /**
     * Apply Layout Styles
     */
    /*
    function applyLayoutStyles(layoutName) {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const widgets = document.querySelectorAll('[data-widget]');

        // Reset all widget styles first
        widgets.forEach(widget => {
            widget.style.display = '';
            widget.style.gridColumn = '';
            widget.style.order = '';
            widget.style.borderLeft = '';
            widget.style.transform = '';
            widget.style.boxShadow = '';
            widget.style.backgroundColor = '';
            widget.style.maxHeight = '';
            widget.style.overflow = '';
            widget.style.border = '';
            widget.style.cursor = '';
            widget.style.position = '';
        });

        // Reset dashboard container styles
        dashboardWidgets.style.display = '';
        dashboardWidgets.style.flexDirection = '';
        dashboardWidgets.style.gap = '';
        dashboardWidgets.style.backgroundColor = '';
        dashboardWidgets.style.padding = '';
        dashboardWidgets.style.borderRadius = '';
        dashboardWidgets.style.boxShadow = '';
        dashboardWidgets.style.border = '';
        dashboardWidgets.style.position = 'relative';
        dashboardWidgets.style.overflow = 'visible';

        // Remove any existing layout label
        const existingLabel = document.getElementById('layout-label');
        if (existingLabel) {
            existingLabel.remove();
        }

        // Apply layout-specific styles
        switch(layoutName) {
            case 'adhd-optimized':
                applyADHDOptimizedStyles();
                break;
            case 'focus':
                applyFocusStyles();
                break;
            case 'standard':
                applyStandardStyles();
                break;
            case 'custom':
                applyCustomStyles();
                break;
        }
    }

    /**
     * Apply ADHD Optimized Styles
     */
    function applyADHDOptimizedStyles() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const widgets = document.querySelectorAll('[data-widget]');
        const currentFocusWidget = document.getElementById('current-focus-widget');

        // Add layout label
        addLayoutLabel('ADHD Optimized Layout', '#0ea5e9');

        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(14, 165, 233, 0.08)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(14, 165, 233, 0.2)';
        dashboardWidgets.style.gap = '1.75rem';

        // Apply widget styles
        widgets.forEach(widget => {
            widget.style.borderLeft = '4px solid transparent';
            widget.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';

            // Apply widget-specific styling
            if (widget.id === 'current-focus-widget') {
                widget.style.gridColumn = '1 / -1';
                widget.style.borderLeftWidth = '6px';
                widget.style.borderLeftColor = 'rgba(14, 165, 233, 0.8)';
                widget.style.transform = 'scale(1.02)';
                widget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
            } else if (widget.getAttribute('data-widget') === 'today-tasks') {
                widget.style.borderLeftColor = 'rgba(59, 130, 246, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'overdue-tasks') {
                widget.style.borderLeftColor = 'rgba(239, 68, 68, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'income-opportunities') {
                widget.style.borderLeftColor = 'rgba(245, 158, 11, 0.8)';
                widget.style.gridColumn = '1 / -1';
            } else if (widget.getAttribute('data-widget') === 'passive-income') {
                widget.style.borderLeftColor = 'rgba(16, 185, 129, 0.8)';
                widget.style.gridColumn = '1 / -1';
            } else if (widget.getAttribute('data-widget') === 'online-business') {
                widget.style.borderLeftColor = 'rgba(6, 182, 212, 0.8)';
                widget.style.gridColumn = '1 / -1';
            } else if (widget.getAttribute('data-widget') === 'ai-agents-widget') {
                widget.style.borderLeftColor = 'rgba(99, 102, 241, 0.8)';
                widget.style.gridColumn = '1 / -1';
            } else if (widget.getAttribute('data-widget') === 'youtube-links') {
                widget.style.borderLeftColor = 'rgba(239, 68, 68, 0.8)';
                widget.style.gridColumn = '1 / -1';
            } else if (widget.getAttribute('data-widget') === 'keyboard-shortcuts') {
                widget.style.borderLeftColor = 'rgba(139, 92, 246, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'adhd-guide') {
                widget.style.borderLeftColor = 'rgba(16, 185, 129, 0.8)';
            } else if (widget.getAttribute('data-widget') === 'help-center') {
                widget.style.borderLeftColor = 'rgba(245, 158, 11, 0.8)';
            }
        });
    }
    */

    /**
     * Apply Focus Styles
     */
    /*
    function applyFocusStyles() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const widgets = document.querySelectorAll('[data-widget]');
        const currentFocusWidget = document.getElementById('current-focus-widget');
        const todayTasksWidget = document.querySelector('[data-widget="today-tasks"]');
        const keyboardShortcutsWidget = document.querySelector('[data-widget="keyboard-shortcuts"]');

        // Add layout label
        addLayoutLabel('Focus Mode', '#6366f1');

        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(99, 102, 241, 0.2)';
        dashboardWidgets.style.display = 'flex';
        dashboardWidgets.style.flexDirection = 'column';
        dashboardWidgets.style.gap = '1.5rem';

        // Apply widget styles
        widgets.forEach(widget => {
            const widgetType = widget.getAttribute('data-widget');

            if (widget.id === 'current-focus-widget' || widgetType === 'today-tasks' || widgetType === 'keyboard-shortcuts') {
                // Show essential widgets
                widget.style.display = 'block';
                widget.style.gridColumn = '1 / -1';

                // Apply widget-specific styling
                if (widget.id === 'current-focus-widget') {
                    widget.style.order = '-1';
                    widget.style.borderLeft = '8px solid rgba(99, 102, 241, 0.8)';
                    widget.style.transform = 'scale(1.03)';
                    widget.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
                    widget.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
                } else if (widgetType === 'today-tasks') {
                    widget.style.order = '0';
                    widget.style.borderLeft = '5px solid rgba(59, 130, 246, 0.8)';
                } else if (widgetType === 'keyboard-shortcuts') {
                    widget.style.order = '1';
                    widget.style.borderLeft = '5px solid rgba(139, 92, 246, 0.8)';
                    widget.style.maxHeight = '200px';
                    widget.style.overflow = 'auto';
                }
            } else {
                // Hide non-essential widgets
                widget.style.display = 'none';
            }
        });
    }
    */

    /**
     * Apply Standard Styles
     */
    /*
    function applyStandardStyles() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const widgets = document.querySelectorAll('[data-widget]');

        // Add layout label
        addLayoutLabel('Standard Layout', '#10b981');

        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
        dashboardWidgets.style.padding = '1.25rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.boxShadow = '0 0 0 2px rgba(16, 185, 129, 0.2)';
        dashboardWidgets.style.gap = '1rem';

        // Apply widget styles
        widgets.forEach(widget => {
            // Reset styles
            widget.style.display = 'block';

            // Apply widget-specific styling
            if (widget.id === 'current-focus-widget') {
                widget.style.gridColumn = '1 / -1';
                widget.style.borderLeft = '4px solid rgba(16, 185, 129, 0.8)';
            } else {
                widget.style.minHeight = '200px';
                widget.style.border = '1px solid rgba(0, 0, 0, 0.05)';
                widget.style.borderRadius = '0.5rem';
            }
        });
    }
    */

    /**
     * Apply Custom Styles
     */
    /*
    function applyCustomStyles() {
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        const widgets = document.querySelectorAll('[data-widget]');
        const widgetControls = document.getElementById('widget-controls');

        // Add layout label
        addLayoutLabel('Custom Layout', '#8b5cf6');

        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
        dashboardWidgets.style.boxShadow = 'none';

        // Apply widget styles
        widgets.forEach(widget => {
            // Reset styles
            widget.style.display = 'block';

            // Apply widget-specific styling
            widget.style.border = '2px dotted rgba(139, 92, 246, 0.3)';
            widget.style.position = 'relative';
            widget.style.cursor = 'move';

            // Add drag handle indicator
            const widgetId = widget.getAttribute('data-widget') || widget.id;
            const handleId = 'drag-handle-' + widgetId;

            // Remove existing handle if any
            const existingHandle = document.getElementById(handleId);
            if (existingHandle) {
                existingHandle.remove();
            }

            // Create new handle
            const handle = document.createElement('div');
            handle.id = handleId;
            handle.innerHTML = '⋮⋮';
            handle.style.position = 'absolute';
            handle.style.top = '10px';
            handle.style.right = '10px';
            handle.style.color = 'rgba(139, 92, 246, 0.5)';
            handle.style.fontSize = '16px';
            handle.style.lineHeight = '1';
            handle.style.cursor = 'move';

            widget.appendChild(handle);
        });

        // Show widget controls
        if (widgetControls) {
            widgetControls.style.display = 'block';
            widgetControls.style.marginBottom = '1.5rem';
            widgetControls.style.padding = '1rem';
            widgetControls.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
            widgetControls.style.borderRadius = '0.5rem';
            widgetControls.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
        }
    }
    */

    /**
     * Add Layout Label
     */
    /*
    function addLayoutLabel(labelText, color) {
        // Remove existing label if any
        const existingLabel = document.getElementById('layout-label');
        if (existingLabel) {
            existingLabel.remove();
        }

        // Create new label
        const label = document.createElement('div');
        label.id = 'layout-label';
        label.textContent = labelText;
        label.style.position = 'absolute';
        label.style.top = '-10px';
        label.style.left = '20px';
        label.style.backgroundColor = color;
        label.style.color = 'white';
        label.style.padding = '2px 10px';
        label.style.borderRadius = '4px';
        label.style.fontSize = '0.75rem';
        label.style.fontWeight = '600';
        label.style.zIndex = '10';

        // Add to dashboard widgets container
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (dashboardWidgets) {
            dashboardWidgets.appendChild(label);
        }
    }
    */

    /**
     * Fix Clear Focus Button
     */
    /*
    function fixClearFocusButton() {
        const clearFocusBtn = document.getElementById('clear-focus-task');

        if (!clearFocusBtn) {
            console.log('Clear focus button not found');
            return;
        }

        console.log('Fixing clear focus button');

        // Remove any existing event listeners
        const newButton = clearFocusBtn.cloneNode(true);
        clearFocusBtn.parentNode.replaceChild(newButton, clearFocusBtn);

        // Add direct click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Clear focus button clicked (direct fix)');

            // Send AJAX request to clear current focus
            fetch('/momentum/dashboard/clear-focus-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Clear focus response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Focus cleared:', data);

                if (data.success) {
                    showSuccessMessage('Focus cleared successfully!');

                    // Reload the page to refresh the widgets
                    window.location.reload();
                } else {
                    console.error('Failed to clear focus:', data.message);
                    alert('Failed to clear focus. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error clearing focus:', error);
                alert('An error occurred. Please try again.');
            });
        });
    }

    /**
     * Fix Focus Mode Toggle
     */
    /*
    function fixFocusModeToggle() {
        const focusModeToggle = document.getElementById('focus-mode-toggle');

        if (!focusModeToggle) {
            console.log('Focus mode toggle not found');
            return;
        }

        console.log('Fixing focus mode toggle');

        // Remove any existing event listeners
        const newToggle = focusModeToggle.cloneNode(true);
        focusModeToggle.parentNode.replaceChild(newToggle, focusModeToggle);

        // Add direct click handler
        newToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Focus mode toggle clicked (direct fix)');

            const markCompleteBtn = document.getElementById('mark-complete-btn');
            const taskId = markCompleteBtn ? markCompleteBtn.getAttribute('data-task-id') : null;

            if (taskId) {
                window.location.href = `/momentum/productivity/focus-mode?task_id=${taskId}`;
            } else {
                alert('Please set a focus task first.');
            }
        });
    }
    */

    /**
     * Fix Mark Complete Button
     */
    /*
    function fixMarkCompleteButton() {
        const markCompleteBtn = document.getElementById('mark-complete-btn');

        if (!markCompleteBtn) {
            console.log('Mark complete button not found');
            return;
        }

        console.log('Fixing mark complete button');

        // Remove any existing event listeners
        const newButton = markCompleteBtn.cloneNode(true);
        markCompleteBtn.parentNode.replaceChild(newButton, markCompleteBtn);

        // Add direct click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const taskId = this.getAttribute('data-task-id');
            console.log('Mark complete button clicked (direct fix):', taskId);

            // Send AJAX request to mark task as complete
            fetch(`/momentum/tasks/complete/${taskId}`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('Mark complete response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Task marked as complete:', data);

                if (data.success) {
                    showSuccessMessage('Task completed successfully!');

                    // Reload the page after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    console.error('Failed to complete task:', data.message);
                    alert('Failed to complete task. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error completing task:', error);
                alert('An error occurred. Please try again.');
            });
        });
    }

    /**
     * Fix Task Checkboxes
     */
    function fixTaskCheckboxes() {
        const taskCheckboxes = document.querySelectorAll('.task-checkbox');

        if (taskCheckboxes.length === 0) {
            console.log('No task checkboxes found');
            return;
        }

        console.log('Fixing task checkboxes:', taskCheckboxes.length);

        taskCheckboxes.forEach((checkbox, index) => {
            // Remove any existing event listeners
            const newCheckbox = checkbox.cloneNode(true);
            checkbox.parentNode.replaceChild(newCheckbox, checkbox);

            // Add direct change handler
            newCheckbox.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');
                const isChecked = this.checked;

                console.log(`Task checkbox ${index + 1} changed (direct fix):`, taskId, isChecked);

                // Send AJAX request to update task status
                fetch(`/momentum/tasks/update-status/${taskId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `status=${isChecked ? 'done' : 'todo'}`
                })
                .then(response => {
                    console.log('Update task status response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Task status updated:', data);

                    if (data.success) {
                        // Update UI
                        const taskItem = document.querySelector(`.task-item[data-task-id="${taskId}"]`);
                        if (taskItem) {
                            const taskTitle = taskItem.querySelector('a');

                            if (isChecked) {
                                taskTitle.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');
                                showSuccessMessage('Task marked as complete!');
                            } else {
                                taskTitle.classList.remove('line-through', 'text-gray-500', 'dark:text-gray-400');
                                showSuccessMessage('Task marked as incomplete');
                            }
                        }
                    } else {
                        console.error('Failed to update task status:', data.message);
                        this.checked = !isChecked; // Revert checkbox state
                        alert('Failed to update task status. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error updating task status:', error);
                    this.checked = !isChecked; // Revert checkbox state
                    alert('An error occurred. Please try again.');
                });
            });
        });
    }
    */

    /**
     * Show Success Message
     */
    /*
    function showSuccessMessage(message) {
        console.log('Showing success message:', message);

        // Create message element if it doesn't exist
        let messageElement = document.getElementById('success-message');

        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'success-message';
            messageElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 transform transition-all duration-300 opacity-0 translate-y-[-20px]';
            document.body.appendChild(messageElement);
        }

        // Set message text
        messageElement.textContent = message;

        // Show message with animation
        setTimeout(() => {
            messageElement.classList.remove('opacity-0', 'translate-y-[-20px]');
            messageElement.classList.add('opacity-100', 'translate-y-0');
        }, 10);

        // Hide message after delay
        setTimeout(() => {
            messageElement.classList.remove('opacity-100', 'translate-y-0');
            messageElement.classList.add('opacity-0', 'translate-y-[-20px]');
        }, 3000);
    }
    */
});
</script>

<!-- Task Management Scripts -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Task Management Scripts loaded');

        // Get elements
        const markCompleteBtn = document.getElementById('mark-complete-btn');
        const taskCheckboxes = document.querySelectorAll('.task-checkbox');
        const clearFocusBtn = document.getElementById('clear-focus-task');

        console.log('Mark complete button:', markCompleteBtn ? 'Found' : 'Not found');
        console.log('Task checkboxes:', taskCheckboxes.length);
        console.log('Clear focus button:', clearFocusBtn ? 'Found' : 'Not found');

        // Function to show success message
        function showSuccessMessage(message) {
            console.log('Showing success message:', message);

            // Create message element if it doesn't exist
            let messageElement = document.getElementById('success-message');

            if (!messageElement) {
                messageElement = document.createElement('div');
                messageElement.id = 'success-message';
                messageElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 transform transition-all duration-300 opacity-0 translate-y-[-20px]';
                document.body.appendChild(messageElement);
            }

            // Set message text
            messageElement.textContent = message;

            // Show message with animation
            setTimeout(() => {
                messageElement.classList.remove('opacity-0', 'translate-y-[-20px]');
            }, 10);

            // Hide message after 3 seconds
            setTimeout(() => {
                messageElement.classList.add('opacity-0', 'translate-y-[-20px]');

                // Remove from DOM after animation completes
                setTimeout(() => {
                    if (messageElement.parentNode) {
                        messageElement.parentNode.removeChild(messageElement);
                    }
                }, 300);
            }, 3000);
        }

        // Note: Task checkboxes are now handled by direct-button-fix.js
        // This code is kept for reference but is no longer active
        /*
        taskCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');
                const isChecked = this.checked;

                console.log('Task checkbox changed:', taskId, isChecked);

                // Send AJAX request to update task status
                fetch(`/momentum/tasks/update-status/${taskId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `status=${isChecked ? 'done' : 'todo'}`
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Task status updated:', data);

                    if (data.success) {
                        // Update UI
                        const taskItem = document.querySelector(`.task-item[data-task-id="${taskId}"]`);
                        const taskTitle = taskItem.querySelector('a');

                        if (isChecked) {
                            taskTitle.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');
                            showSuccessMessage('Task marked as complete!');
                        } else {
                            taskTitle.classList.remove('line-through', 'text-gray-500', 'dark:text-gray-400');
                            showSuccessMessage('Task marked as incomplete');
                        }
                    } else {
                        console.error('Failed to update task status:', data.message);
                        this.checked = !isChecked; // Revert checkbox state
                    }
                })
                .catch(error => {
                    console.error('Error updating task status:', error);
                    this.checked = !isChecked; // Revert checkbox state
                });
            });
        });
        */

        // Note: Mark complete button is now handled by direct-button-fix.js
        // This code is kept for reference but is no longer active
        /*
        if (markCompleteBtn) {
            markCompleteBtn.addEventListener('click', function(e) {
                e.preventDefault();

                const taskId = this.getAttribute('data-task-id');
                console.log('Mark complete button clicked for task:', taskId);

                // Send AJAX request to mark task as complete
                fetch(`/momentum/tasks/complete/${taskId}`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Task marked as complete:', data);

                    if (data.success) {
                        showSuccessMessage('Task completed successfully!');

                        // Redirect to dashboard after a short delay
                        setTimeout(() => {
                            window.location.href = '/momentum/dashboard';
                        }, 1000);
                    } else {
                        console.error('Failed to complete task:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error completing task:', error);
                });
            });
        }
        */

        // Note: Clear focus button is now handled by focus-button-handler.js
        // This code is kept for reference but is no longer active
        /*
        if (clearFocusBtn) {
            clearFocusBtn.addEventListener('click', function(e) {
                e.preventDefault();

                console.log('Clear focus button clicked');

                // Send AJAX request to clear current focus
                fetch('/momentum/dashboard/clear-focus-task', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Focus cleared:', data);

                    if (data.success) {
                        showSuccessMessage('Focus cleared successfully!');

                        // Redirect to dashboard after a short delay
                        setTimeout(() => {
                            window.location.href = '/momentum/dashboard';
                        }, 1000);
                    } else {
                        console.error('Failed to clear focus:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error clearing focus:', error);
                });
            });
        }
        */

        // Note: Focus mode toggle is now handled by focus-button-handler.js
        // This code is kept for reference but is no longer active
        /*
        const focusModeToggle = document.getElementById('focus-mode-toggle');
        if (focusModeToggle) {
            focusModeToggle.addEventListener('click', function() {
                console.log('Focus mode toggle clicked');

                // Toggle focus mode class on body
                document.body.classList.toggle('focus-mode');

                // Update button text
                if (document.body.classList.contains('focus-mode')) {
                    this.innerHTML = '<i class="fas fa-compress mr-1.5"></i> Exit Focus Mode';
                    showSuccessMessage('Entered focus mode');
                } else {
                    this.innerHTML = '<i class="fas fa-expand mr-1.5"></i> Enter Focus Mode';
                    showSuccessMessage('Exited focus mode');
                }
            });
        }
        */

        // Handle keyboard shortcuts toggle
        const toggleShortcutsDetails = document.getElementById('toggle-shortcuts-details');
        const shortcutsDetails = document.getElementById('shortcuts-details');

        if (toggleShortcutsDetails && shortcutsDetails) {
            toggleShortcutsDetails.addEventListener('click', function() {
                const isHidden = shortcutsDetails.classList.contains('hidden');

                if (isHidden) {
                    shortcutsDetails.classList.remove('hidden');
                    this.textContent = 'Hide Details';
                } else {
                    shortcutsDetails.classList.add('hidden');
                    this.textContent = 'Show Details';
                }
            });
        }

        // Handle set focus buttons
        const setFocusButtons = document.querySelectorAll('.set-focus-btn');
        setFocusButtons.forEach(button => {
            button.addEventListener('click', function() {
                const taskId = this.getAttribute('data-task-id');
                console.log('Set focus button clicked for task:', taskId);

                // Redirect to set focus page
                window.location.href = `/momentum/tasks/set-focus/${taskId}`;
            });
        });

        // Handle keyboard shortcuts for AI Assistant
        document.addEventListener('keydown', function(e) {
            // Only trigger if Alt key is pressed and no input is focused
            if (e.altKey && !e.ctrlKey && !e.shiftKey && !e.metaKey) {
                const activeElement = document.activeElement;
                const isInputFocused = activeElement.tagName === 'INPUT' ||
                                     activeElement.tagName === 'TEXTAREA' ||
                                     activeElement.contentEditable === 'true';

                if (!isInputFocused) {
                    switch(e.key.toLowerCase()) {
                        case 't':
                            e.preventDefault();
                            window.location.href = '/momentum/tasks';
                            break;
                        case 'p':
                            e.preventDefault();
                            window.location.href = '/momentum/projects';
                            break;
                        case 'c':
                            e.preventDefault();
                            window.location.href = '/momentum/checklists';
                            break;
                        case 'b':
                            e.preventDefault();
                            window.location.href = '/momentum/business';
                            break;
                        case 'a':
                            e.preventDefault();
                            window.location.href = '/momentum/ai-agents';
                            break;
                        case 'f':
                            e.preventDefault();
                            window.location.href = '/momentum/financial';
                            break;
                        case 'd':
                            e.preventDefault();
                            window.location.href = '/momentum/dashboard';
                            break;
                        case 'q':
                            e.preventDefault();
                            // Open Quick Capture modal if it exists, otherwise go to page
                            if (typeof openQuickCapture === 'function') {
                                openQuickCapture();
                            } else {
                                window.location.href = '/momentum/quick-capture';
                            }
                            break;
                        case 's':
                            e.preventDefault();
                            // Open screenshot tool
                            window.open('/momentum/quick-capture/screenshot', '_blank', 'width=1200,height=800');
                            break;
                        case 'n':
                            e.preventDefault();
                            // Go to quick note creation
                            window.location.href = '/momentum/quick-capture/note';
                            break;
                        case 'i':
                            e.preventDefault();
                            // Go to AI prompts
                            window.location.href = '/momentum/ai-prompts';
                            break;
                        case 'y':
                            e.preventDefault();
                            // Go to YouTube Links
                            window.location.href = '/momentum/tools/youtube-links';
                            break;
                    }
                }
            }
        });
    });
</script>

<!-- Layout Button Complete Fix -->
<script src="/momentum/js/layout-button-complete-fix.js"></script>
