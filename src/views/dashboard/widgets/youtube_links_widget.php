<?php
/**
 * YouTube Links Widget
 *
 * This widget displays YouTube links on the main dashboard
 */

// Ensure we have YouTube Links data
if (!isset($youtubeLinkData)) {
    $youtubeLinkData = [
        'stats' => ['total_links' => 0, 'to_watch' => 0, 'completed' => 0, 'high_priority' => 0],
        'recentLinks' => [],
        'collections' => []
    ];
}

$stats = $youtubeLinkData['stats'];
$recentLinks = $youtubeLinkData['recentLinks'];
$collections = $youtubeLinkData['collections'];
?>

<!-- YouTube Links Widget -->
<div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg" data-widget="youtube-links" style="height: 400px; max-height: 400px; overflow: hidden; display: flex; flex-direction: column;">
    <div class="px-5 py-5" style="display: flex; flex-direction: column; height: 100%; overflow: hidden;">
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center">
                <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fab fa-youtube text-red-500 mr-2"></i> YouTube Links
                </h2>
                <span class="ml-3 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full">
                    Organized
                </span>
            </div>
            <div class="flex space-x-2">
                <button onclick="openYouTubeQuickAdd()" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300" title="Quick Add">
                    <i class="fas fa-plus"></i>
                </button>
                <a href="/momentum/tools/youtube-links" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" title="View All">
                    <i class="fas fa-external-link-alt"></i>
                </a>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-4 gap-2 mb-4">
            <div class="text-center">
                <div class="text-lg font-semibold text-gray-900 dark:text-white"><?= $stats['total_links'] ?></div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Total</div>
            </div>
            <div class="text-center">
                <div class="text-lg font-semibold text-yellow-600 dark:text-yellow-400"><?= $stats['to_watch'] ?></div>
                <div class="text-xs text-gray-500 dark:text-gray-400">To Watch</div>
            </div>
            <div class="text-center">
                <div class="text-lg font-semibold text-green-600 dark:text-green-400"><?= $stats['completed'] ?></div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Done</div>
            </div>
            <div class="text-center">
                <div class="text-lg font-semibold text-red-600 dark:text-red-400"><?= $stats['high_priority'] ?></div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Priority</div>
            </div>
        </div>

        <!-- Recent Links -->
        <div class="flex-1 overflow-hidden">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Recent Links</h4>
            <div class="space-y-2 overflow-y-auto" style="max-height: 200px;">
                <?php if (empty($recentLinks)): ?>
                    <div class="text-center py-4">
                        <i class="fab fa-youtube text-gray-300 dark:text-gray-600 text-3xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">No YouTube links yet</p>
                        <button onclick="openYouTubeQuickAdd()" class="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 mt-1">
                            Add your first link
                        </button>
                    </div>
                <?php else: ?>
                    <?php foreach ($recentLinks as $link): ?>
                        <div class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <?php if ($link['thumbnail_url']): ?>
                                <img src="<?= htmlspecialchars($link['thumbnail_url']) ?>" 
                                     alt="Thumbnail" 
                                     class="w-12 h-8 object-cover rounded">
                            <?php else: ?>
                                <div class="w-12 h-8 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                                    <i class="fab fa-youtube text-gray-400 text-sm"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    <?= htmlspecialchars($link['title'] ?: 'Untitled Video') ?>
                                </p>
                                <div class="flex items-center space-x-2">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                                        <?= htmlspecialchars($link['channel_name'] ?: 'Unknown Channel') ?>
                                    </p>
                                    <?php if ($link['priority'] === 'high'): ?>
                                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                            <i class="fas fa-exclamation text-xs mr-1"></i>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-1">
                                <!-- Status indicator -->
                                <div class="w-2 h-2 rounded-full 
                                    <?php if ($link['status'] === 'completed'): ?>
                                        bg-green-500
                                    <?php elseif ($link['status'] === 'watching'): ?>
                                        bg-purple-500
                                    <?php else: ?>
                                        bg-yellow-500
                                    <?php endif; ?>"
                                    title="<?= ucfirst($link['status']) ?>">
                                </div>
                                
                                <!-- Quick actions -->
                                <a href="<?= htmlspecialchars($link['url']) ?>" 
                                   target="_blank" 
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-xs"
                                   title="Watch Video">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Collections Summary -->
        <?php if (!empty($collections)): ?>
            <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Collections</h4>
                <div class="flex flex-wrap gap-1">
                    <?php foreach (array_slice($collections, 0, 3) as $collection): ?>
                        <a href="/momentum/tools/youtube-links/collection/<?= $collection['id'] ?>" 
                           class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium hover:opacity-80 transition-opacity"
                           style="background-color: <?= htmlspecialchars($collection['color']) ?>20; color: <?= htmlspecialchars($collection['color']) ?>">
                            <i class="<?= htmlspecialchars($collection['icon']) ?> mr-1"></i>
                            <?= htmlspecialchars($collection['name']) ?>
                            <span class="ml-1 text-xs opacity-75">(<?= $collection['link_count'] ?>)</span>
                        </a>
                    <?php endforeach; ?>
                    <?php if (count($collections) > 3): ?>
                        <a href="/momentum/tools/youtube-links" 
                           class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:opacity-80">
                            +<?= count($collections) - 3 ?> more
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Add Modal for YouTube Links -->
<div id="youtubeQuickAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Add YouTube Link</h3>
            </div>
            <form id="youtubeQuickAddForm" class="p-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">YouTube URL</label>
                        <input type="url" id="youtubeQuickUrl" required 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white"
                               placeholder="https://www.youtube.com/watch?v=...">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Priority</label>
                        <select id="youtubeQuickPriority" 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeYouTubeQuickAdd()" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md">
                        Add Link
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openYouTubeQuickAdd() {
    const modal = document.getElementById('youtubeQuickAddModal');
    if (modal) {
        modal.classList.remove('hidden');
        document.getElementById('youtubeQuickUrl').focus();
    }
}

function closeYouTubeQuickAdd() {
    const modal = document.getElementById('youtubeQuickAddModal');
    if (modal) {
        modal.classList.add('hidden');
        document.getElementById('youtubeQuickAddForm').reset();
    }
}

// Handle form submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('youtubeQuickAddForm');
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const url = document.getElementById('youtubeQuickUrl').value;
            const priority = document.getElementById('youtubeQuickPriority').value;

            if (!url) {
                alert('Please enter a YouTube URL');
                return;
            }

            try {
                const response = await fetch('/momentum/tools/youtube-links/quick-add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: url,
                        priority: priority
                    })
                });

                const result = await response.json();

                if (result.success) {
                    closeYouTubeQuickAdd();
                    // Refresh the page to show the new link
                    window.location.reload();
                } else {
                    alert(result.message || 'Failed to add link');
                }
            } catch (error) {
                console.error('Error adding link:', error);
                alert('An error occurred while adding the link');
            }
        });
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        const modal = document.getElementById('youtubeQuickAddModal');
        if (modal && e.target === modal) {
            closeYouTubeQuickAdd();
        }
    });
});
</script>
