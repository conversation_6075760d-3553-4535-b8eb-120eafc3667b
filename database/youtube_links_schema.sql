-- YouTube Links Management Schema

-- Create youtube_link_collections table
CREATE TABLE IF NOT EXISTS youtube_link_collections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6',
    icon VARCHAR(50) DEFAULT 'fas fa-folder',
    sort_order INT DEFAULT 0,
    is_default BOOLEAN DEFAULT FALSE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create youtube_links table
CREATE TABLE IF NOT EXISTS youtube_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    collection_id INT,
    youtube_id VARCHAR(50),
    url TEXT NOT NULL,
    title VARCHAR(255),
    channel_name VA<PERSON>HA<PERSON>(255),
    duration VARCHAR(20),
    thumbnail_url TEXT,
    description TEXT,
    tags JSON,
    status ENUM('to_watch', 'watching', 'completed', 'archived') DEFAULT 'to_watch',
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    watch_progress INT DEFAULT 0, -- seconds watched
    personal_notes TEXT,
    added_from VARCHAR(50) DEFAULT 'manual', -- manual, import, extension, research
    metadata JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (collection_id) REFERENCES youtube_link_collections(id) ON DELETE SET NULL,
    INDEX idx_user_status (user_id, status),
    INDEX idx_user_priority (user_id, priority),
    INDEX idx_youtube_id (youtube_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create youtube_link_analytics table
CREATE TABLE IF NOT EXISTS youtube_link_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    link_id INT NOT NULL,
    action_type ENUM('viewed', 'completed', 'noted', 'shared') NOT NULL,
    action_data JSON,
    created_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (link_id) REFERENCES youtube_links(id) ON DELETE CASCADE,
    INDEX idx_user_action (user_id, action_type),
    INDEX idx_link_action (link_id, action_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default collections
INSERT INTO youtube_link_collections (user_id, name, description, color, icon, sort_order, is_default, created_at, updated_at) 
SELECT 
    id as user_id,
    'Watch Later' as name,
    'Videos to watch when you have time' as description,
    '#3B82F6' as color,
    'fas fa-clock' as icon,
    0 as sort_order,
    TRUE as is_default,
    NOW() as created_at,
    NOW() as updated_at
FROM users 
WHERE NOT EXISTS (
    SELECT 1 FROM youtube_link_collections 
    WHERE youtube_link_collections.user_id = users.id 
    AND youtube_link_collections.is_default = TRUE
);

INSERT INTO youtube_link_collections (user_id, name, description, color, icon, sort_order, is_default, created_at, updated_at) 
SELECT 
    id as user_id,
    'Learning' as name,
    'Educational and tutorial videos' as description,
    '#10B981' as color,
    'fas fa-graduation-cap' as icon,
    1 as sort_order,
    FALSE as is_default,
    NOW() as created_at,
    NOW() as updated_at
FROM users 
WHERE NOT EXISTS (
    SELECT 1 FROM youtube_link_collections 
    WHERE youtube_link_collections.user_id = users.id 
    AND youtube_link_collections.name = 'Learning'
);

INSERT INTO youtube_link_collections (user_id, name, description, color, icon, sort_order, is_default, created_at, updated_at) 
SELECT 
    id as user_id,
    'Business Ideas' as name,
    'Videos about business opportunities and strategies' as description,
    '#F59E0B' as color,
    'fas fa-lightbulb' as icon,
    2 as sort_order,
    FALSE as is_default,
    NOW() as created_at,
    NOW() as updated_at
FROM users 
WHERE NOT EXISTS (
    SELECT 1 FROM youtube_link_collections 
    WHERE youtube_link_collections.user_id = users.id 
    AND youtube_link_collections.name = 'Business Ideas'
);
