<?php
/**
 * Create Research & Planning Tables Step by Step
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    echo "Connected to database successfully.\n\n";

    // Create tables one by one with error handling
    $tables = [
        'research_sessions' => "
            CREATE TABLE research_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                research_type ENUM('market_analysis', 'technical_research', 'competitive_analysis', 'trend_analysis', 'other') DEFAULT 'other',
                status ENUM('active', 'completed', 'paused', 'archived') DEFAULT 'active',
                priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                tags JSON,
                metadata JSON,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                completed_at DATETIME NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'research_links' => "
            CREATE TABLE research_links (
                id INT AUTO_INCREMENT PRIMARY KEY,
                research_session_id INT NOT NULL,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                url TEXT NOT NULL,
                description TEXT,
                link_type ENUM('article', 'documentation', 'tool', 'competitor', 'reference', 'video', 'other') DEFAULT 'other',
                importance ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
                status ENUM('to_review', 'reviewed', 'implemented', 'archived') DEFAULT 'to_review',
                notes TEXT,
                tags JSON,
                metadata JSON,
                access_count INT DEFAULT 0,
                last_accessed DATETIME NULL,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'research_notes' => "
            CREATE TABLE research_notes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                research_session_id INT NOT NULL,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                note_type ENUM('finding', 'insight', 'question', 'action_item', 'summary', 'other') DEFAULT 'finding',
                importance ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
                tags JSON,
                linked_links JSON,
                metadata JSON,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'research_plans' => "
            CREATE TABLE research_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                research_session_id INT NOT NULL,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                plan_type ENUM('implementation', 'strategy', 'roadmap', 'experiment', 'other') DEFAULT 'implementation',
                priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                estimated_effort ENUM('small', 'medium', 'large', 'xl') DEFAULT 'medium',
                estimated_duration_days INT DEFAULT NULL,
                success_criteria TEXT,
                risks_and_mitigation TEXT,
                resources_needed TEXT,
                status ENUM('draft', 'ready', 'in_progress', 'completed', 'cancelled') DEFAULT 'draft',
                progress INT DEFAULT 0,
                tags JSON,
                metadata JSON,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'plan_action_items' => "
            CREATE TABLE plan_action_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                research_plan_id INT NOT NULL,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                action_type ENUM('research', 'development', 'testing', 'documentation', 'communication', 'other') DEFAULT 'other',
                priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                estimated_time_minutes INT DEFAULT NULL,
                dependencies JSON,
                status ENUM('todo', 'in_progress', 'blocked', 'completed', 'cancelled') DEFAULT 'todo',
                assigned_to INT NULL,
                due_date DATE NULL,
                notes TEXT,
                completion_notes TEXT,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                completed_at DATETIME NULL,
                FOREIGN KEY (research_plan_id) REFERENCES research_plans(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'research_project_conversions' => "
            CREATE TABLE research_project_conversions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                research_session_id INT NOT NULL,
                research_plan_id INT NULL,
                project_id INT NOT NULL,
                user_id INT NOT NULL,
                conversion_type ENUM('full_project', 'partial_implementation', 'experiment', 'prototype') DEFAULT 'full_project',
                conversion_notes TEXT,
                success_metrics TEXT,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (research_plan_id) REFERENCES research_plans(id) ON DELETE SET NULL,
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];

    $createdTables = [];
    $failedTables = [];

    foreach ($tables as $tableName => $sql) {
        echo "Creating table: $tableName\n";
        
        try {
            // Check if table already exists
            $exists = $db->fetchOne("SHOW TABLES LIKE ?", [$tableName]);
            
            if ($exists) {
                echo "  ✓ Table '$tableName' already exists\n";
                $createdTables[] = $tableName;
                continue;
            }
            
            // Create the table
            $result = $db->query($sql);
            
            if ($result !== false) {
                echo "  ✓ Table '$tableName' created successfully\n";
                $createdTables[] = $tableName;
            } else {
                echo "  ✗ Failed to create table '$tableName'\n";
                $failedTables[] = $tableName;
            }
            
        } catch (Exception $e) {
            echo "  ✗ Error creating table '$tableName': " . $e->getMessage() . "\n";
            $failedTables[] = $tableName;
        }
        
        echo "\n";
    }

    // Create indexes
    echo "Creating indexes...\n";
    
    $indexes = [
        "CREATE INDEX idx_research_sessions_user_status ON research_sessions(user_id, status)",
        "CREATE INDEX idx_research_sessions_type ON research_sessions(research_type)",
        "CREATE INDEX idx_research_links_session ON research_links(research_session_id)",
        "CREATE INDEX idx_research_links_status ON research_links(status)",
        "CREATE INDEX idx_research_notes_session ON research_notes(research_session_id)",
        "CREATE INDEX idx_research_plans_session ON research_plans(research_session_id)",
        "CREATE INDEX idx_research_plans_status ON research_plans(status)",
        "CREATE INDEX idx_plan_action_items_plan ON plan_action_items(research_plan_id)",
        "CREATE INDEX idx_plan_action_items_status ON plan_action_items(status)"
    ];

    foreach ($indexes as $index) {
        try {
            $db->query($index);
            echo "  ✓ Index created\n";
        } catch (Exception $e) {
            echo "  ✗ Index creation failed: " . $e->getMessage() . "\n";
        }
    }

    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Table Creation Summary:\n";
    echo "Created: " . count($createdTables) . " tables\n";
    echo "Failed: " . count($failedTables) . " tables\n";
    
    if (!empty($createdTables)) {
        echo "\nSuccessfully created tables:\n";
        foreach ($createdTables as $table) {
            echo "  - $table\n";
        }
    }
    
    if (!empty($failedTables)) {
        echo "\nFailed to create tables:\n";
        foreach ($failedTables as $table) {
            echo "  - $table\n";
        }
    }

    // Create sample data if tables were created successfully
    if (count($createdTables) >= 4) {
        echo "\nCreating sample data...\n";
        
        try {
            // Create sample research session
            $sessionSql = "INSERT INTO research_sessions (user_id, title, description, research_type, status, priority, tags, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $sessionData = [
                1, // user_id
                'AI Agent Army Market Research',
                'Research into AI agent platforms, competitors, and market opportunities for building our own platform',
                'market_analysis',
                'active',
                'high',
                json_encode(['ai', 'agents', 'market-research', 'competition']),
                json_encode(['created_by' => 'system', 'sample_data' => true]),
                date('Y-m-d H:i:s'),
                date('Y-m-d H:i:s')
            ];
            
            $sessionId = $db->insert($sessionSql, $sessionData);
            
            if ($sessionId) {
                echo "  ✓ Sample research session created (ID: $sessionId)\n";
                
                // Create sample links
                $linkSql = "INSERT INTO research_links (research_session_id, user_id, title, url, description, link_type, importance, status, notes, tags, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                $sampleLinks = [
                    [
                        $sessionId, 1,
                        'CrewAI - Multi-Agent Framework',
                        'https://www.crewai.com/',
                        'Leading multi-agent platform for business automation',
                        'competitor', 'high', 'to_review', '',
                        json_encode(['competitor', 'multi-agent']),
                        json_encode(['sample_data' => true]),
                        date('Y-m-d H:i:s'), date('Y-m-d H:i:s')
                    ],
                    [
                        $sessionId, 1,
                        'Microsoft AutoGen Documentation',
                        'https://microsoft.github.io/autogen/',
                        'Microsoft\'s conversational AI agent framework',
                        'documentation', 'high', 'to_review', '',
                        json_encode(['microsoft', 'documentation']),
                        json_encode(['sample_data' => true]),
                        date('Y-m-d H:i:s'), date('Y-m-d H:i:s')
                    ]
                ];
                
                foreach ($sampleLinks as $linkData) {
                    $linkId = $db->insert($linkSql, $linkData);
                    if ($linkId) {
                        echo "  ✓ Sample link created: " . $linkData[2] . "\n";
                    }
                }
                
                // Create sample plan
                $planSql = "INSERT INTO research_plans (research_session_id, user_id, title, description, plan_type, priority, estimated_effort, estimated_duration_days, success_criteria, status, progress, tags, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                $planData = [
                    $sessionId, 1,
                    'AI Agent Army Implementation Plan',
                    'Comprehensive plan to implement competitive AI agent platform with ADHD-friendly features',
                    'implementation',
                    'high',
                    'large',
                    90,
                    'Launch MVP with core features, acquire first 100 users, generate $10k MRR',
                    'draft',
                    0,
                    json_encode(['implementation', 'mvp', 'ai-agents']),
                    json_encode(['sample_data' => true]),
                    date('Y-m-d H:i:s'),
                    date('Y-m-d H:i:s')
                ];
                
                $planId = $db->insert($planSql, $planData);
                
                if ($planId) {
                    echo "  ✓ Sample research plan created (ID: $planId)\n";
                }
            }
            
        } catch (Exception $e) {
            echo "  ✗ Error creating sample data: " . $e->getMessage() . "\n";
        }
    }

    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Research & Planning System Setup Complete!\n";
    echo str_repeat("=", 50) . "\n\n";

    echo "✅ Database tables created successfully!\n";
    echo "✅ Sample data added for testing\n";
    echo "✅ System ready for integration\n\n";

    echo "Next steps:\n";
    echo "1. Add navigation menu item\n";
    echo "2. Include routes in main system\n";
    echo "3. Test the research dashboard\n";
    echo "4. Start your first research session!\n\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
