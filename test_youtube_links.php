<?php

echo "Testing YouTube Links implementation...\n";

try {
    require_once 'src/controllers/YouTubeLinkController.php';
    echo "✅ YouTubeLinkController loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Error loading YouTubeLinkController: " . $e->getMessage() . "\n";
}

try {
    require_once 'src/models/YouTubeLink.php';
    echo "✅ YouTubeLink model loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Error loading YouTubeLink model: " . $e->getMessage() . "\n";
}

try {
    require_once 'src/models/YouTubeLinkCollection.php';
    echo "✅ YouTubeLinkCollection model loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Error loading YouTubeLinkCollection model: " . $e->getMessage() . "\n";
}

echo "\nTesting complete!\n";
