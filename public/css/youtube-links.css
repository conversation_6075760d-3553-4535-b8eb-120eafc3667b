/**
 * YouTube Links CSS
 * Styles for YouTube links management
 */

.link-item {
    transition: all 0.2s ease-in-out;
}

.link-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.bulk-checkbox {
    transition: all 0.3s ease-in-out;
}

.bulk-checkbox.hidden {
    opacity: 0;
    transform: translateX(-20px);
}

.bulk-checkbox:not(.hidden) {
    opacity: 1;
    transform: translateX(0);
}

#bulk-actions-bar {
    transition: all 0.3s ease-in-out;
}

#bulk-actions-bar.hidden {
    opacity: 0;
    transform: translateY(-10px);
}

#bulk-actions-bar:not(.hidden) {
    opacity: 1;
    transform: translateY(0);
}

/* YouTube thumbnail styling */
.link-item img {
    transition: transform 0.2s ease-in-out;
}

.link-item:hover img {
    transform: scale(1.05);
}

/* Status badges */
.status-badge {
    transition: all 0.2s ease-in-out;
}

/* Priority indicators */
.priority-high {
    border-left: 4px solid #ef4444;
}

.priority-medium {
    border-left: 4px solid #f59e0b;
}

.priority-low {
    border-left: 4px solid #10b981;
}

/* Collection color indicators */
.collection-indicator {
    width: 4px;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 0 4px 4px 0;
}

/* Modal animations */
#quickAddModal {
    animation: fadeIn 0.3s ease-out;
}

#quickAddModal .bg-white {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .link-item {
        padding: 12px;
    }
    
    .link-item .flex {
        flex-direction: column;
        space-y: 3;
    }
    
    .link-item img,
    .link-item .w-24 {
        width: 100%;
        height: auto;
        max-width: 200px;
        margin: 0 auto 12px;
    }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
    .link-item:hover {
        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
    }
}

/* Focus states for accessibility */
.link-item:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

button:focus,
input:focus,
select:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
