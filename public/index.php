<?php
/**
 * Main Entry Point
 *
 * This file serves as the entry point for the application.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', dirname(__DIR__));

// Define application root path for views
define('APPROOT', BASE_PATH . '/src');

// Autoload classes
spl_autoload_register(function ($className) {
    $paths = [
        BASE_PATH . '/src/controllers/' . $className . '.php',
        BASE_PATH . '/src/models/' . $className . '.php',
        BASE_PATH . '/src/utils/' . $className . '.php'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

// Load utilities
require_once BASE_PATH . '/src/utils/Session.php';
require_once BASE_PATH . '/src/utils/Router.php';

// Load controllers
require_once BASE_PATH . '/src/controllers/BaseController.php';
require_once BASE_PATH . '/src/controllers/OnlineBusinessController.php';
require_once BASE_PATH . '/src/controllers/MedicalController.php';
require_once BASE_PATH . '/src/controllers/AIAgentController.php';
require_once BASE_PATH . '/src/controllers/BrigadeTemplateController.php';
require_once BASE_PATH . '/src/controllers/ChecklistController.php';
require_once BASE_PATH . '/src/controllers/AIPromptController.php';
require_once BASE_PATH . '/src/controllers/QuickCaptureController.php';
require_once BASE_PATH . '/src/controllers/AstrologyController.php';
require_once BASE_PATH . '/src/controllers/ResearchPlanningController.php';
try {
    require_once BASE_PATH . '/src/controllers/YouTubeAgentController.php';
} catch (Exception $e) {
    // Log the error but don't let it crash the main site
    error_log('Error loading YouTubeAgentController: ' . $e->getMessage());
}
try {
    require_once BASE_PATH . '/src/controllers/YouTubeLinkController.php';
} catch (Exception $e) {
    // Log the error but don't let it crash the main site
    error_log('Error loading YouTubeLinkController: ' . $e->getMessage());
}
try {
    require_once BASE_PATH . '/src/controllers/CloneController.php';
} catch (Exception $e) {
    // Log the error but don't let it crash the main site
    error_log('Error loading CloneController: ' . $e->getMessage());
}

// Load database
require_once BASE_PATH . '/src/utils/Database.php';

// Load models
require_once BASE_PATH . '/src/models/BusinessVenture.php';
require_once BASE_PATH . '/src/models/BusinessMetric.php';
require_once BASE_PATH . '/src/models/AIAgent.php';
require_once BASE_PATH . '/src/models/AIAgentCategory.php';
require_once BASE_PATH . '/src/models/AIAgentTask.php';
require_once BASE_PATH . '/src/models/AIAgentInteraction.php';
require_once BASE_PATH . '/src/models/AIPrompt.php';
require_once BASE_PATH . '/src/models/AIPromptCategory.php';
require_once BASE_PATH . '/src/models/QuickCapture.php';

// Start session
Session::start();

// Create router
$router = new Router();

// Define routes

// Home route
$router->get('/', function() {
    if (Session::isLoggedIn()) {
        header('Location: /momentum/dashboard');
        exit;
    } else {
        header('Location: /momentum/login');
        exit;
    }
});

// Auth routes
$router->get('/login', function() {
    $controller = new AuthController();
    $controller->showLogin();
});

$router->post('/login', function() {
    $controller = new AuthController();
    $controller->login();
});

$router->get('/register', function() {
    $controller = new AuthController();
    $controller->showRegister();
});

$router->post('/register', function() {
    $controller = new AuthController();
    $controller->register();
});

$router->get('/logout', function() {
    $controller = new AuthController();
    $controller->logout();
});

$router->get('/forgot-password', function() {
    $controller = new AuthController();
    $controller->showForgotPassword();
});

$router->post('/forgot-password', function() {
    $controller = new AuthController();
    $controller->forgotPassword();
});

// Dashboard routes
$router->get('/dashboard', function() {
    $controller = new DashboardController();
    $controller->index();
});

// Redirect old redesign URL to main dashboard for backward compatibility
$router->get('/dashboard/redesign', function() {
    header('Location: /momentum/dashboard');
    exit;
});

$router->post('/dashboard/update-layout', function() {
    $controller = new DashboardController();
    $controller->updateLayout();
});

$router->get('/dashboard/toggle-theme', function() {
    $controller = new DashboardController();
    $controller->toggleTheme();
});

$router->post('/dashboard/set-focus-task', function() {
    $controller = new DashboardController();
    $controller->setFocusTask();
});

$router->post('/dashboard/clear-focus-task', function() {
    $controller = new DashboardController();
    $controller->clearFocusTask();
});

// Task routes
$router->get('/tasks', function() {
    $controller = new TaskController();
    $controller->index();
});

$router->get('/tasks/calendar', function() {
    $controller = new TaskController();
    $controller->calendar();
});

$router->get('/tasks/create', function() {
    $controller = new TaskController();
    $controller->create();
});

$router->post('/tasks/create', function() {
    $controller = new TaskController();
    $controller->store();
});

$router->get('/tasks/view/:id', function($id) {
    $controller = new TaskController();
    $controller->viewTask($id);
});

$router->get('/tasks/edit/:id', function($id) {
    $controller = new TaskController();
    $controller->edit($id);
});

$router->post('/tasks/edit/:id', function($id) {
    $controller = new TaskController();
    $controller->update($id);
});

$router->get('/tasks/delete/:id', function($id) {
    $controller = new TaskController();
    $controller->delete($id);
});

$router->get('/tasks/complete/:id', function($id) {
    $controller = new TaskController();
    $controller->complete($id);
});

$router->get('/tasks/set-focus/:id', function($id) {
    $controller = new TaskController();
    $controller->setFocus($id);
});

$router->get('/tasks/ajax', function() {
    $controller = new TaskController();
    $controller->ajax();
});

// Idea routes
$router->get('/ideas', function() {
    $controller = new IdeaController();
    $controller->index();
});

$router->get('/ideas/create', function() {
    $controller = new IdeaController();
    $controller->create();
});

$router->post('/ideas/create', function() {
    $controller = new IdeaController();
    $controller->store();
});

$router->get('/ideas/view/:id', function($id) {
    $controller = new IdeaController();
    $controller->viewIdea($id);
});

$router->get('/ideas/edit/:id', function($id) {
    $controller = new IdeaController();
    $controller->edit($id);
});

$router->post('/ideas/edit/:id', function($id) {
    $controller = new IdeaController();
    $controller->update($id);
});

$router->get('/ideas/delete/:id', function($id) {
    $controller = new IdeaController();
    $controller->delete($id);
});

$router->get('/ideas/convert/:id', function($id) {
    $controller = new IdeaController();
    $controller->showConvert($id);
});

$router->post('/ideas/convert/:id', function($id) {
    $controller = new IdeaController();
    $controller->convert($id);
});

$router->post('/ideas/quick-save', function() {
    $controller = new IdeaController();
    $controller->quickSave();
});

// Finance routes
$router->get('/finances', function() {
    $controller = new FinanceController();
    $controller->index();
});

$router->get('/finances/create-transaction', function() {
    $controller = new FinanceController();
    $controller->createTransaction();
});

$router->post('/finances/create-transaction', function() {
    $controller = new FinanceController();
    $controller->storeTransaction();
});

$router->get('/finances/edit-transaction/:id', function($id) {
    $controller = new FinanceController();
    $controller->editTransaction($id);
});

$router->post('/finances/edit-transaction/:id', function($id) {
    $controller = new FinanceController();
    $controller->updateTransaction($id);
});

$router->post('/finances/delete-transaction/:id', function($id) {
    $controller = new FinanceController();
    $controller->deleteTransaction($id);
});

// Receipt management routes
$router->get('/finances/transactions/:id/receipts', function($id) {
    $controller = new FinanceController();
    $controller->viewReceipts($id);
});

$router->get('/finances/receipts/:id/delete', function($id) {
    $controller = new FinanceController();
    $controller->deleteReceipt($id);
});

$router->get('/finances/receipts', function() {
    $controller = new FinanceController();
    $controller->viewAllReceipts();
});

// Debt routes
$router->get('/finances/debts', function() {
    $controller = new DebtController();
    $controller->index();
});

$router->get('/finances/create-debt', function() {
    $controller = new DebtController();
    $controller->createDebt();
});

$router->post('/finances/create-debt', function() {
    $controller = new DebtController();
    $controller->storeDebt();
});

$router->get('/finances/edit-debt/:id', function($id) {
    $controller = new DebtController();
    $controller->editDebt($id);
});

$router->post('/finances/edit-debt/:id', function($id) {
    $controller = new DebtController();
    $controller->updateDebt($id);
});

$router->post('/finances/record-payment/:id', function($id) {
    $controller = new DebtController();
    $controller->recordPayment($id);
});

$router->post('/finances/delete-debt/:id', function($id) {
    $controller = new DebtController();
    $controller->deleteDebt($id);
});

$router->get('/finances/subscriptions', function() {
    $controller = new FinanceController();
    $controller->subscriptions();
});

$router->get('/finances/create-subscription', function() {
    $controller = new FinanceController();
    $controller->createSubscription();
});

$router->post('/finances/create-subscription', function() {
    $controller = new FinanceController();
    $controller->storeSubscription();
});

$router->get('/finances/subscription-details/:id', function($id) {
    $controller = new FinanceController();
    $controller->subscriptionDetails($id);
});

$router->get('/finances/edit-subscription/:id', function($id) {
    $controller = new FinanceController();
    $controller->editSubscription($id);
});

$router->post('/finances/edit-subscription/:id', function($id) {
    $controller = new FinanceController();
    $controller->updateSubscription($id);
});

$router->get('/finances/delete-subscription/:id', function($id) {
    $controller = new FinanceController();
    $controller->deleteSubscription($id);
});

// Budget routes
$router->get('/finances/budgets', function() {
    $controller = new BudgetController();
    $controller->index();
});

$router->get('/finances/budgets/create', function() {
    $controller = new BudgetController();
    $controller->createBudget();
});

$router->post('/finances/budgets/create', function() {
    $controller = new BudgetController();
    $controller->storeBudget();
});

$router->get('/finances/budgets/view/:id', function($id) {
    $controller = new BudgetController();
    $controller->viewBudget($id);
});

$router->get('/finances/budgets/edit/:id', function($id) {
    $controller = new BudgetController();
    $controller->editBudget($id);
});

$router->post('/finances/budgets/update/:id', function($id) {
    $controller = new BudgetController();
    $controller->updateBudget($id);
});

$router->post('/finances/budgets/add-category/:id', function($id) {
    $controller = new BudgetController();
    $controller->addBudgetCategory($id);
});

$router->post('/finances/budgets/update-category/:id', function($id) {
    $controller = new BudgetController();
    $controller->updateBudgetCategory($id);
});

$router->post('/finances/budgets/delete-category/:id', function($id) {
    $controller = new BudgetController();
    $controller->deleteBudgetCategory($id);
});

$router->get('/finances/budgets/delete/:id', function($id) {
    $controller = new BudgetController();
    $controller->deleteBudget($id);
});

$router->get('/finances/budgets/reports', function() {
    $controller = new BudgetController();
    $controller->reports();
});

// Income Sources routes
$router->get('/finances/income-sources', function() {
    $controller = new IncomeSourceController();
    $controller->index();
});

$router->get('/finances/income-sources/create', function() {
    $controller = new IncomeSourceController();
    $controller->create();
});

$router->post('/finances/income-sources/create', function() {
    $controller = new IncomeSourceController();
    $controller->store();
});

$router->get('/finances/income-sources/view/:id', function($id) {
    $controller = new IncomeSourceController();
    $controller->viewSource($id);
});

$router->get('/finances/income-sources/edit/:id', function($id) {
    $controller = new IncomeSourceController();
    $controller->edit($id);
});

$router->post('/finances/income-sources/update/:id', function($id) {
    $controller = new IncomeSourceController();
    $controller->update($id);
});

$router->post('/finances/income-sources/delete/:id', function($id) {
    $controller = new IncomeSourceController();
    $controller->delete($id);
});

// Fallback route for GET requests to delete endpoint
$router->get('/finances/income-sources/delete/:id', function($id) {
    $controller = new IncomeSourceController();
    $controller->handleGetDelete($id);
});

// Income forecast route
$router->get('/finances/income-sources/forecast', function() {
    $controller = new IncomeSourceController();
    $controller->forecast();
});

// Financial Goals routes
$router->get('/finances/goals', function() {
    $controller = new FinancialGoalController();
    $controller->index();
});

$router->get('/finances/goals/create', function() {
    $controller = new FinancialGoalController();
    $controller->create();
});

$router->post('/finances/goals/create', function() {
    $controller = new FinancialGoalController();
    $controller->store();
});

$router->get('/finances/goals/view/:id', function($id) {
    $controller = new FinancialGoalController();
    $controller->viewGoal($id);
});

$router->get('/finances/goals/edit/:id', function($id) {
    $controller = new FinancialGoalController();
    $controller->edit($id);
});

$router->post('/finances/goals/edit/:id', function($id) {
    $controller = new FinancialGoalController();
    $controller->update($id);
});

$router->get('/finances/goals/delete/:id', function($id) {
    $controller = new FinancialGoalController();
    $controller->delete($id);
});

$router->get('/finances/goals/contribution/:id', function($id) {
    $controller = new FinancialGoalController();
    $controller->createContribution($id);
});

$router->post('/finances/goals/contribution/:id', function($id) {
    $controller = new FinancialGoalController();
    $controller->storeContribution($id);
});

$router->post('/finances/income-sources/link-transaction', function() {
    $controller = new IncomeSourceController();
    $controller->linkTransaction();
});

$router->post('/finances/income-sources/unlink-transaction', function() {
    $controller = new IncomeSourceController();
    $controller->unlinkTransaction();
});

// Note routes
$router->get('/notes', function() {
    $controller = new NoteController();
    $controller->index();
});

$router->get('/notes/create', function() {
    $controller = new NoteController();
    $controller->create();
});

$router->post('/notes/create', function() {
    $controller = new NoteController();
    $controller->store();
});

$router->get('/notes/view/:id', function($id) {
    $controller = new NoteController();
    $controller->viewNote($id);
});

$router->get('/notes/edit/:id', function($id) {
    $controller = new NoteController();
    $controller->edit($id);
});

$router->post('/notes/edit/:id', function($id) {
    $controller = new NoteController();
    $controller->update($id);
});

$router->get('/notes/delete/:id', function($id) {
    $controller = new NoteController();
    $controller->delete($id);
});

$router->get('/notes/search', function() {
    $controller = new NoteController();
    $controller->search();
});

$router->post('/notes/toggle-pin/:id', function($id) {
    $controller = new NoteController();
    $controller->togglePin($id);
});

$router->post('/notes/toggle-favorite/:id', function($id) {
    $controller = new NoteController();
    $controller->toggleFavorite($id);
});

$router->post('/notes/auto-save', function() {
    $controller = new NoteController();
    $controller->autoSave();
});

$router->get('/notes/templates', function() {
    $controller = new NoteController();
    $controller->templates();
});

// Phase 2: Template Management Routes
$router->get('/notes/create-template', function() {
    $controller = new NoteController();
    $controller->createTemplate();
});

$router->post('/notes/create-template', function() {
    $controller = new NoteController();
    $controller->createTemplate();
});

$router->post('/notes/create-from-template', function() {
    $controller = new NoteController();
    $controller->createFromTemplate();
});

// Phase 2: Advanced Search
$router->get('/notes/advanced-search', function() {
    $controller = new NoteController();
    $controller->advancedSearch();
});

// Phase 2: Bulk Operations
$router->post('/notes/bulk-action', function() {
    $controller = new NoteController();
    $controller->bulkAction();
});

// Productivity routes
$router->get('/productivity/focus-timer', function() {
    $controller = new ProductivityController();
    $controller->focusTimer();
});

$router->get('/productivity/focus-mode', function() {
    $controller = new ProductivityController();
    $controller->focusMode();
});

$router->post('/productivity/log-focus-session', function() {
    $controller = new ProductivityController();
    $controller->logFocusSession();
});

// Time Blocking routes
$router->get('/productivity/time-blocking', function() {
    $controller = new ProductivityController();
    $controller->timeBlocking();
});

$router->post('/productivity/create-time-block', function() {
    $controller = new ProductivityController();
    $controller->createTimeBlock();
});

$router->post('/productivity/update-time-block/:id', function($id) {
    $controller = new ProductivityController();
    $controller->updateTimeBlock($id);
});

$router->get('/productivity/delete-time-block/:id', function($id) {
    $controller = new ProductivityController();
    $controller->deleteTimeBlock($id);
});

$router->get('/productivity/widgets/today-time-blocks', function() {
    $controller = new ProductivityController();
    $controller->getTodayTimeBlocksWidget();
});

$router->get('/productivity/widgets/energy-level', function() {
    $controller = new ProductivityController();
    $controller->getEnergyLevelWidget();
});

$router->get('/productivity/time-block/:id', function($id) {
    $controller = new ProductivityController();
    $controller->getTimeBlock($id);
});

$router->post('/productivity/update-time-block-time/:id', function($id) {
    $controller = new ProductivityController();
    $controller->updateTimeBlockTime($id);
});

$router->post('/productivity/resize-time-block/:id', function($id) {
    $controller = new ProductivityController();
    $controller->resizeTimeBlock($id);
});

// Energy Level Tracking routes
$router->get('/productivity/energy-tracking', function() {
    $controller = new ProductivityController();
    $controller->energyTracking();
});

$router->post('/productivity/record-energy-level', function() {
    $controller = new ProductivityController();
    $controller->recordEnergyLevel();
});

$router->post('/productivity/update-energy-level/:id', function($id) {
    $controller = new ProductivityController();
    $controller->updateEnergyLevel($id);
});

$router->get('/productivity/delete-energy-level/:id', function($id) {
    $controller = new ProductivityController();
    $controller->deleteEnergyLevel($id);
});

// Task Batching routes
$router->get('/productivity/task-batching', function() {
    $controller = new ProductivityController();
    $controller->taskBatching();
});

$router->post('/productivity/create-batch', function() {
    $controller = new ProductivityController();
    $controller->createBatch();
});

$router->get('/productivity/view-batch/:id', function($id) {
    $controller = new ProductivityController();
    $controller->viewBatch($id);
});

$router->post('/productivity/update-batch/:id', function($id) {
    $controller = new ProductivityController();
    $controller->updateBatch($id);
});

$router->get('/productivity/delete-batch/:id', function($id) {
    $controller = new ProductivityController();
    $controller->deleteBatch($id);
});

$router->post('/productivity/add-task-to-batch/:id', function($id) {
    $controller = new ProductivityController();
    $controller->addTaskToBatch($id);
});

$router->get('/productivity/remove-task-from-batch/:batchId/:taskId', function($batchId, $taskId) {
    $controller = new ProductivityController();
    $controller->removeTaskFromBatch($batchId, $taskId);
});

$router->post('/productivity/reorder-batch-tasks/:id', function($id) {
    $controller = new ProductivityController();
    $controller->reorderBatchTasks($id);
});

$router->post('/productivity/schedule-batch/:id', function($id) {
    $controller = new ProductivityController();
    $controller->scheduleBatch($id);
});

$router->get('/productivity/widgets/task-batch', function() {
    $controller = new ProductivityController();
    $controller->getTaskBatchWidget();
});

// Batch Templates routes
$router->get('/productivity/batch-templates', function() {
    $controller = new ProductivityController();
    $controller->batchTemplates();
});

$router->post('/productivity/create-template', function() {
    $controller = new ProductivityController();
    $controller->createTemplate();
});

$router->get('/productivity/view-template/:id', function($id) {
    $controller = new ProductivityController();
    $controller->viewTemplate($id);
});

$router->post('/productivity/update-template/:id', function($id) {
    $controller = new ProductivityController();
    $controller->updateTemplate($id);
});

$router->get('/productivity/delete-template/:id', function($id) {
    $controller = new ProductivityController();
    $controller->deleteTemplate($id);
});

$router->post('/productivity/add-item-to-template/:id', function($id) {
    $controller = new ProductivityController();
    $controller->addItemToTemplate($id);
});

$router->get('/productivity/remove-item-from-template/:templateId/:itemId', function($templateId, $itemId) {
    $controller = new ProductivityController();
    $controller->removeItemFromTemplate($templateId, $itemId);
});

$router->post('/productivity/reorder-template-items/:id', function($id) {
    $controller = new ProductivityController();
    $controller->reorderTemplateItems($id);
});

$router->get('/productivity/create-batch-from-template/:id', function($id) {
    $controller = new ProductivityController();
    $controller->createBatchFromTemplate($id);
});

$router->get('/productivity/generate-recurring-batches', function() {
    $controller = new ProductivityController();
    $controller->generateRecurringBatches();
});

// Productivity Tools Dashboard
$router->get('/productivity/tools', function() {
    // Redirect to the productivity dashboard
    header('Location: /momentum/productivity_dashboard.php');
    exit;
});

// Tools routes
$router->get('/tools', function() {
    $controller = new ToolsController();
    $controller->index();
});

$router->get('/tools/currency-converter', function() {
    $controller = new ToolsController();
    $controller->currencyConverter();
});

$router->post('/tools/get-currency-rate', function() {
    $controller = new ToolsController();
    $controller->getCurrencyRate();
});

// API Authentication Check
$router->get('/api/auth/check', function() {
    header('Content-Type: application/json');

    // Check if user is logged in
    require_once __DIR__ . '/../src/utils/Session.php';

    $isAuthenticated = Session::isLoggedIn();
    $user = $isAuthenticated ? Session::getUser() : null;

    echo json_encode([
        'authenticated' => $isAuthenticated,
        'user' => $user ? [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email']
        ] : null
    ]);
});

// AI Assistant Tools routes
$router->get('/tools/ai-prompts', function() {
    $controller = new AIPromptController();
    $controller->index();
});

$router->get('/tools/quick-capture', function() {
    $controller = new QuickCaptureController();
    $controller->index();
});

$router->get('/tools/screenshot', function() {
    $controller = new QuickCaptureController();
    $controller->screenshot();
});

$router->get('/tools/note', function() {
    $controller = new QuickCaptureController();
    $controller->noteForm();
});

$router->get('/tools/voice', function() {
    $controller = new QuickCaptureController();
    $controller->voiceForm();
});

$router->get('/tools/gallery', function() {
    $controller = new QuickCaptureController();
    $controller->gallery();
});

// YouTube Links routes
$router->get('/tools/youtube-links', function() {
    $controller = new YouTubeLinkController();
    $controller->index();
});

$router->get('/tools/youtube-links/add', function() {
    $controller = new YouTubeLinkController();
    $controller->addLink();
});

$router->post('/tools/youtube-links/add', function() {
    $controller = new YouTubeLinkController();
    $controller->addLink();
});

$router->post('/tools/youtube-links/quick-add', function() {
    $controller = new YouTubeLinkController();
    $controller->quickAdd();
});

$router->post('/tools/youtube-links/update-status', function() {
    $controller = new YouTubeLinkController();
    $controller->updateStatus();
});

$router->get('/tools/youtube-links/collection/:id', function($id) {
    $controller = new YouTubeLinkController();
    $controller->viewCollection($id);
});

$router->get('/tools/youtube-links/search', function() {
    $controller = new YouTubeLinkController();
    $controller->search();
});

$router->get('/tools/youtube-links/dashboard-data', function() {
    $controller = new YouTubeLinkController();
    $controller->getDashboardData();
});

$router->post('/tools/youtube-links/bulk-action', function() {
    $controller = new YouTubeLinkController();
    $controller->bulkAction();
});

// Quick Capture routes
$router->get('/quick-capture', function() {
    $controller = new QuickCaptureController();
    $controller->index();
});

$router->get('/quick-capture/gallery', function() {
    $controller = new QuickCaptureController();
    $controller->gallery();
});

$router->get('/quick-capture/screenshot', function() {
    $controller = new QuickCaptureController();
    $controller->screenshot();
});

$router->post('/quick-capture/screenshot/upload', function() {
    $controller = new QuickCaptureController();
    $controller->uploadScreenshot();
});

$router->get('/quick-capture/note', function() {
    $controller = new QuickCaptureController();
    $controller->noteForm();
});

$router->post('/quick-capture/note/create', function() {
    $controller = new QuickCaptureController();
    $controller->createNote();
});

$router->get('/quick-capture/voice', function() {
    $controller = new QuickCaptureController();
    $controller->voiceForm();
});

$router->post('/quick-capture/voice/upload', function() {
    $controller = new QuickCaptureController();
    $controller->uploadVoice();
});

$router->get('/quick-capture/view/:id', function($id) {
    $controller = new QuickCaptureController();
    $controller->viewCapture($id);
});

// Astrology routes
$router->get('/astrology', function() {
    $controller = new AstrologyController();
    $controller->index();
});

$router->get('/astrology/rahu-kalaya', function() {
    $controller = new AstrologyController();
    $controller->rahuKalaya();
});

$router->get('/astrology/info', function() {
    $controller = new AstrologyController();
    $controller->info();
});

$router->get('/astrology/api/current-status', function() {
    $controller = new AstrologyController();
    $controller->getCurrentStatus();
});

$router->post('/astrology/preferences', function() {
    $controller = new AstrologyController();
    $controller->savePreferences();
});

// ADHD routes
$router->get('/adhd', function() {
    $controller = new ADHDController();
    $controller->index();
});

$router->get('/adhd/guide', function() {
    $controller = new ADHDController();
    $controller->guide();
});

// ADHD Symptom Tracking routes
$router->get('/adhd/symptom-tracker', function() {
    $controller = new ADHDController();
    $controller->symptomTracker();
});

$router->get('/adhd/symptom-tracker/log', function() {
    $controller = new ADHDController();
    $controller->logSymptoms();
});

$router->post('/adhd/symptom-tracker/log', function() {
    $controller = new ADHDController();
    $controller->saveSymptomLog();
});

$router->get('/adhd/symptom-tracker/event', function() {
    $controller = new ADHDController();
    $controller->logEvent();
});

$router->post('/adhd/symptom-tracker/event', function() {
    $controller = new ADHDController();
    $controller->saveEvent();
});

$router->get('/adhd/symptom-tracker/reports', function() {
    $controller = new ADHDController();
    $controller->viewReports();
});

// ADHD CBT routes
$router->get('/adhd/cbt/thought-records', function() {
    $controller = new ADHDController();
    $controller->thoughtRecords();
});

$router->get('/adhd/cbt/thought-records/new', function() {
    $controller = new ADHDController();
    $controller->newThoughtRecord();
});

$router->post('/adhd/cbt/thought-records/new', function() {
    $controller = new ADHDController();
    $controller->saveThoughtRecord();
});

$router->post('/adhd/cbt/thought-records/save', function() {
    $controller = new ADHDController();
    $controller->saveThoughtRecord();
});

$router->get('/adhd/cbt/thought-records/view/:id', function($id) {
    $controller = new ADHDController();
    $controller->viewThoughtRecord($id);
});

// ADHD Productivity Strategies routes
$router->get('/adhd/productivity/strategies', function() {
    $controller = new ADHDController();
    $controller->strategies();
});

$router->get('/adhd/productivity/strategies/view/:id', function($id) {
    $controller = new ADHDController();
    $controller->viewStrategy($id);
});

$router->post('/adhd/productivity/strategies/add', function() {
    $controller = new ADHDController();
    $controller->addStrategy();
});

$router->post('/adhd/productivity/strategies/update-effectiveness', function() {
    $controller = new ADHDController();
    $controller->updateStrategyEffectiveness();
});

$router->get('/adhd/productivity/strategies/deactivate/:id', function($id) {
    $controller = new ADHDController();
    $controller->deactivateStrategy($id);
});

// ADHD Mindfulness & Emotional Regulation routes
$router->get('/adhd/mindfulness', function() {
    $controller = new ADHDController();
    $controller->mindfulness();
});

$router->get('/adhd/mindfulness/exercise/:id', function($id) {
    $controller = new ADHDController();
    $controller->viewExercise($id);
});

$router->get('/adhd/mindfulness/practice/:id', function($id) {
    $controller = new ADHDController();
    $controller->practiceExercise($id);
});

$router->post('/adhd/mindfulness/log-practice', function() {
    $controller = new ADHDController();
    $controller->logPractice();
});

$router->get('/adhd/mindfulness/quick-exercises', function() {
    $controller = new ADHDController();
    $controller->quickExercises();
});

// ADHD Consistency Tracking routes
$router->get('/adhd/consistency/trackers', function() {
    $controller = new ADHDController();
    $controller->consistencyTrackers();
});

$router->get('/adhd/consistency/trackers/new', function() {
    $controller = new ADHDController();
    $controller->newTracker();
});

$router->post('/adhd/consistency/trackers/new', function() {
    $controller = new ADHDController();
    $controller->saveTracker();
});

$router->get('/adhd/consistency/trackers/view/:id', function($id) {
    $controller = new ADHDController();
    $controller->viewTracker($id);
});

$router->post('/adhd/consistency/trackers/log', function() {
    $controller = new ADHDController();
    $controller->logConsistency();
});

$router->get('/adhd/consistency/trackers/delete/:id', function($id) {
    $controller = new ADHDController();
    $controller->deleteTracker($id);
});

// ADHD Medication Tracker routes
$router->get('/adhd/medication', function() {
    $controller = new ADHDController();
    $controller->medications();
});

$router->get('/adhd/medication/new', function() {
    $controller = new ADHDController();
    $controller->newMedication();
});

$router->post('/adhd/medication/save', function() {
    $controller = new ADHDController();
    $controller->saveMedication();
});

$router->get('/adhd/medication/view/:id', function($id) {
    $controller = new ADHDController();
    $controller->viewMedication($id);
});

$router->get('/adhd/medication/edit/:id', function($id) {
    $controller = new ADHDController();
    $controller->editMedication($id);
});

$router->post('/adhd/medication/update/:id', function($id) {
    $controller = new ADHDController();
    $controller->updateMedication($id);
});

$router->get('/adhd/medication/delete/:id', function($id) {
    $controller = new ADHDController();
    $controller->deleteMedication($id);
});

$router->get('/adhd/medication/log', function() {
    $controller = new ADHDController();
    $controller->logMedication();
});

$router->get('/adhd/medication/log/:id', function($id) {
    $controller = new ADHDController();
    $controller->logMedicationById($id);
});

$router->post('/adhd/medication/log/save', function() {
    $controller = new ADHDController();
    $controller->saveMedicationLog();
});

$router->get('/adhd/medication/log/edit/:id', function($id) {
    $controller = new ADHDController();
    $controller->editMedicationLog($id);
});

$router->post('/adhd/medication/log/update/:id', function($id) {
    $controller = new ADHDController();
    $controller->updateMedicationLog($id);
});

$router->get('/adhd/medication/log/delete/:id', function($id) {
    $controller = new ADHDController();
    $controller->deleteMedicationLog($id);
});

$router->get('/adhd/medication/reminder/new/:id', function($id) {
    $controller = new ADHDController();
    $controller->newMedicationReminder($id);
});

$router->post('/adhd/medication/reminder/save', function() {
    $controller = new ADHDController();
    $controller->saveMedicationReminder();
});

$router->get('/adhd/medication/reminder/edit/:id', function($id) {
    $controller = new ADHDController();
    $controller->editMedicationReminder($id);
});

$router->post('/adhd/medication/reminder/update/:id', function($id) {
    $controller = new ADHDController();
    $controller->updateMedicationReminder($id);
});

$router->get('/adhd/medication/reminder/delete/:id', function($id) {
    $controller = new ADHDController();
    $controller->deleteMedicationReminder($id);
});

$router->get('/adhd/medication/reports', function() {
    $controller = new ADHDController();
    $controller->medicationReports();
});

// ADHD Medication Reference routes
$router->get('/adhd/medication/reference', function() {
    $controller = new ADHDController();
    $controller->medicationReference();
});

$router->get('/adhd/medication/reference/view/:id', function($id) {
    $controller = new ADHDController();
    $controller->viewMedicationReference($id);
});

$router->get('/adhd/medication/reference/search', function() {
    $controller = new ADHDController();
    $controller->searchMedicationReference();
});

$router->get('/adhd/medication/suggestions', function() {
    $controller = new ADHDController();
    $controller->getMedicationSuggestions();
});

// ADHD Trigger Identification routes
$router->get('/adhd/triggers', function() {
    $controller = new ADHDController();
    $controller->triggers();
});

$router->get('/adhd/triggers/new', function() {
    $controller = new ADHDController();
    $controller->newTrigger();
});

$router->post('/adhd/triggers/save', function() {
    $controller = new ADHDController();
    $controller->saveTrigger();
});

$router->get('/adhd/triggers/view/:id', function($id) {
    $controller = new ADHDController();
    $controller->viewTrigger($id);
});

$router->get('/adhd/triggers/occurrence', function() {
    $controller = new ADHDController();
    $controller->logTriggerOccurrence();
});

$router->get('/adhd/triggers/occurrence/:id', function($id) {
    $controller = new ADHDController();
    $controller->logTriggerOccurrence($id);
});

$router->post('/adhd/triggers/occurrence/save', function() {
    $controller = new ADHDController();
    $controller->saveTriggerOccurrence();
});

$router->post('/adhd/triggers/save-occurrence', function() {
    $controller = new ADHDController();
    $controller->saveTriggerOccurrence();
});

$router->get('/adhd/triggers/strategy/:id', function($id) {
    $controller = new ADHDController();
    $controller->addCopingStrategy($id);
});

$router->post('/adhd/triggers/strategy/save', function() {
    $controller = new ADHDController();
    $controller->saveCopingStrategy();
});

$router->get('/adhd/triggers/occurrence/edit/:id', function($id) {
    $controller = new ADHDController();
    $controller->editTriggerOccurrence($id);
});

$router->post('/adhd/triggers/occurrence/update/:id', function($id) {
    $controller = new ADHDController();
    $controller->updateTriggerOccurrence($id);
});

// ADHD Executive Function Exercises routes
$router->get('/adhd/executive-function', function() {
    $controller = new ADHDController();
    $controller->executiveFunctionExercises();
});

$router->get('/adhd/executive-function/view/:id', function($id) {
    $controller = new ADHDController();
    $controller->viewExecutiveExercise($id);
});

$router->get('/adhd/executive-function/practice/:id', function($id) {
    $controller = new ADHDController();
    $controller->practiceExecutiveExercise($id);
});

$router->post('/adhd/executive-function/save-result', function() {
    $controller = new ADHDController();
    $controller->saveExerciseResult();
});

$router->get('/adhd/executive-function/progress', function() {
    $controller = new ADHDController();
    $controller->viewProgress();
});

// Project Management routes
$router->get('/projects', function() {
    $controller = new ProjectController();
    $controller->index();
});

$router->get('/projects/create', function() {
    $controller = new ProjectController();
    $controller->create();
});

$router->post('/projects/create', function() {
    $controller = new ProjectController();
    $controller->store();
});

$router->get('/projects/view/:id', function($id) {
    $controller = new ProjectController();
    $controller->viewProject($id);
});

$router->get('/projects/edit/:id', function($id) {
    $controller = new ProjectController();
    $controller->edit($id);
});

$router->post('/projects/edit/:id', function($id) {
    $controller = new ProjectController();
    $controller->update($id);
});

$router->get('/projects/delete/:id', function($id) {
    $controller = new ProjectController();
    $controller->delete($id);
});

$router->post('/projects/:id/add-task', function($id) {
    $controller = new ProjectController();
    $controller->addTask($id);
});

// Task Dependencies API routes
$router->post('/api/task-dependencies/add', function() {
    $controller = new TaskDependencyController();
    $controller->addDependency();
});

$router->post('/api/task-dependencies/delete', function() {
    $controller = new TaskDependencyController();
    $controller->deleteDependency();
});

$router->get('/api/task-dependencies/project/:id', function($id) {
    $controller = new TaskDependencyController();
    $controller->getProjectDependencies($id);
});

// Project Members API routes
$router->post('/api/project-members/add', function() {
    $controller = new ProjectMemberController();
    $controller->addMember();
});

$router->post('/api/project-members/change-role', function() {
    $controller = new ProjectMemberController();
    $controller->changeRole();
});

$router->post('/api/project-members/remove', function() {
    $controller = new ProjectMemberController();
    $controller->removeMember();
});

// Project Comments API routes
$router->post('/api/project-comments/add', function() {
    $controller = new ProjectCommentController();
    $controller->addComment();
});

$router->post('/api/project-comments/edit', function() {
    $controller = new ProjectCommentController();
    $controller->editComment();
});

$router->post('/api/project-comments/delete', function() {
    $controller = new ProjectCommentController();
    $controller->deleteComment();
});

// Financial Report routes
$router->get('/finances/reports', function() {
    $controller = new FinancialReportController();
    $controller->index();
});

$router->get('/finances/reports/income-vs-expenses', function() {
    $controller = new FinancialReportController();
    $controller->incomeVsExpenses();
});

$router->get('/finances/reports/category-analysis', function() {
    $controller = new FinancialReportController();
    $controller->categoryAnalysis();
});

$router->get('/finances/reports/income-source-analysis', function() {
    $controller = new FinancialReportController();
    $controller->incomeSourceAnalysis();
});

$router->get('/finances/reports/year-over-year', function() {
    $controller = new FinancialReportController();
    $controller->yearOverYear();
});

$router->get('/finances/reports/month-over-month', function() {
    $controller = new FinancialReportController();
    $controller->monthOverMonth();
});

$router->get('/finances/reports/advanced-dashboard', function() {
    $controller = new FinancialReportController();
    $controller->advancedDashboard();
});

$router->get('/finances/reports/financial-insights', function() {
    $controller = new FinancialReportController();
    $controller->financialInsights();
});

$router->get('/finances/reports/export/:type/csv', function($type) {
    $controller = new FinancialReportController();
    $controller->exportCSV($type);
});

$router->get('/finances/reports/export/:type/pdf', function($type) {
    $controller = new FinancialReportController();
    $controller->exportPDF($type);
});

// Project Report routes
$router->get('/reports', function() {
    $controller = new ReportController();
    $controller->index();
});

$router->get('/reports/project-progress/:id', function($id) {
    $controller = new ReportController();
    $controller->projectProgress($id);
});

$router->get('/reports/team-workload/:id', function($id) {
    $controller = new ReportController();
    $controller->teamWorkload($id);
});

$router->get('/reports/deadline-compliance/:id', function($id) {
    $controller = new ReportController();
    $controller->deadlineCompliance($id);
});

// Report Export routes
$router->get('/reports/export/project-progress/:id/csv', function($id) {
    $controller = new ReportController();
    $controller->exportProjectProgressCSV($id);
});

$router->get('/reports/export/team-workload/:id/csv', function($id) {
    $controller = new ReportController();
    $controller->exportTeamWorkloadCSV($id);
});

$router->get('/reports/export/deadline-compliance/:id/csv', function($id) {
    $controller = new ReportController();
    $controller->exportDeadlineComplianceCSV($id);
});

// PDF Export routes
$router->get('/reports/export/project-progress/:id/pdf', function($id) {
    $controller = new ReportController();
    $controller->exportProjectProgressPDF($id);
});

$router->get('/reports/export/team-workload/:id/pdf', function($id) {
    $controller = new ReportController();
    $controller->exportTeamWorkloadPDF($id);
});

$router->get('/reports/export/deadline-compliance/:id/pdf', function($id) {
    $controller = new ReportController();
    $controller->exportDeadlineCompliancePDF($id);
});

// Help Center routes
$router->get('/help', function() {
    $controller = new HelpController();
    $controller->index();
});

$router->get('/help/user-guide', function() {
    $controller = new HelpController();
    $controller->userGuide();
});

$router->get('/help/feature-overview', function() {
    $controller = new HelpController();
    $controller->featureOverview();
});

$router->get('/help/tutorials', function() {
    $controller = new HelpController();
    $controller->tutorials();
});

$router->get('/help/faq', function() {
    $controller = new HelpController();
    $controller->faq();
});

$router->get('/help/troubleshooting', function() {
    $controller = new HelpController();
    $controller->troubleshooting();
});

$router->get('/help/contact-support', function() {
    $controller = new HelpController();
    $controller->contactSupport();
});

$router->get('/help/adhd-project-planning-guide', function() {
    $controller = new HelpController();
    $controller->adhdProjectPlanningGuide();
});

$router->get('/help/adhd-project-planning-guide-md', function() {
    $controller = new HelpController();
    $controller->adhdProjectPlanningGuideMd();
});

$router->get('/help/feature-request', function() {
    $controller = new HelpController();
    $controller->featureRequest();
});

// Feature Category Pages
$router->get('/help/mind-mapping-features', function() {
    $controller = new HelpController();
    $controller->mindMappingFeatures();
});

$router->get('/help/adhd-features', function() {
    $controller = new HelpController();
    $controller->adhdFeatures();
});

$router->get('/help/productivity-features', function() {
    $controller = new HelpController();
    $controller->productivityFeatures();
});

$router->get('/help/wellness-features', function() {
    $controller = new HelpController();
    $controller->wellnessFeatures();
});

$router->get('/help/financial-features', function() {
    $controller = new HelpController();
    $controller->financialFeatures();
});

$router->get('/help/knowledge-features', function() {
    $controller = new HelpController();
    $controller->knowledgeFeatures();
});

$router->get('/help/resource-features', function() {
    $controller = new HelpController();
    $controller->resourceFeatures();
});

$router->get('/help/digital-asset-features', function() {
    $controller = new HelpController();
    $controller->digitalAssetFeatures();
});

$router->get('/help/health-features', function() {
    $controller = new HelpController();
    $controller->healthFeatures();
});

$router->get('/help/analytics-features', function() {
    $controller = new HelpController();
    $controller->analyticsFeatures();
});

$router->get('/help/ai-assistance-features', function() {
    $controller = new HelpController();
    $controller->aiAssistanceFeatures();
});

$router->get('/help/online-income-features', function() {
    $controller = new HelpController();
    $controller->onlineIncomeFeatures();
});

$router->get('/help/online-income-strategies-guide', function() {
    $controller = new HelpController();
    $controller->onlineIncomeStrategiesGuide();
});

$router->get('/help/income-stream-evaluator-guide', function() {
    $controller = new HelpController();
    $controller->incomeStreamEvaluatorGuide();
});

$router->get('/help/online-income-strategies-guide-md', function() {
    $controller = new HelpController();
    $controller->onlineIncomeStrategiesGuideMd();
});

$router->get('/help/income-stream-evaluator-guide-md', function() {
    $controller = new HelpController();
    $controller->incomeStreamEvaluatorGuideMd();
});

$router->get('/help/online-income-features', function() {
    $controller = new HelpController();
    $controller->onlineIncomeFeatures();
});

$router->get('/help/freelance-features', function() {
    $controller = new HelpController();
    $controller->freelanceFeatures();
});

$router->get('/help/online-business-features', function() {
    $controller = new HelpController();
    $controller->onlineBusinessFeatures();
});

$router->get('/help/medical-reports', function() {
    $controller = new HelpController();
    $controller->medicalReports();
});

// Income Opportunity Explorer routes
$router->get('/income-opportunities', function() {
    $controller = new IncomeOpportunityController();
    $controller->index();
});

$router->get('/income-opportunities/create', function() {
    $controller = new IncomeOpportunityController();
    $controller->create();
});

$router->post('/income-opportunities/store', function() {
    $controller = new IncomeOpportunityController();
    $controller->store();
});

$router->get('/income-opportunities/view/:id', function($id) {
    $controller = new IncomeOpportunityController();
    $controller->viewOpportunity($id);
});

$router->get('/income-opportunities/edit/:id', function($id) {
    $controller = new IncomeOpportunityController();
    $controller->edit($id);
});

$router->post('/income-opportunities/update/:id', function($id) {
    $controller = new IncomeOpportunityController();
    $controller->update($id);
});

$router->post('/income-opportunities/delete/:id', function($id) {
    $controller = new IncomeOpportunityController();
    $controller->delete($id);
});

$router->get('/income-opportunities/log-activity/:id', function($id) {
    $controller = new IncomeOpportunityController();
    $controller->logActivity($id);
});

$router->post('/income-opportunities/save-activity-log/:id', function($id) {
    $controller = new IncomeOpportunityController();
    $controller->saveActivityLog($id);
});

// Passive Income Portfolio routes
$router->get('/passive-income', function() {
    $controller = new PassiveIncomeController();
    $controller->index();
});

$router->get('/passive-income/create', function() {
    $controller = new PassiveIncomeController();
    $controller->create();
});

$router->post('/passive-income/store', function() {
    $controller = new PassiveIncomeController();
    $controller->store();
});

$router->get('/passive-income/view/:id', function($id) {
    $controller = new PassiveIncomeController();
    $controller->viewStream($id);
});

$router->get('/passive-income/edit/:id', function($id) {
    $controller = new PassiveIncomeController();
    $controller->edit($id);
});

$router->post('/passive-income/update/:id', function($id) {
    $controller = new PassiveIncomeController();
    $controller->update($id);
});

$router->post('/passive-income/delete/:id', function($id) {
    $controller = new PassiveIncomeController();
    $controller->delete($id);
});

$router->post('/passive-income/add-earning/:id', function($id) {
    $controller = new PassiveIncomeController();
    $controller->addEarning($id);
});

$router->post('/passive-income/add-maintenance/:id', function($id) {
    $controller = new PassiveIncomeController();
    $controller->addMaintenance($id);
});

// Income Stream Evaluator routes
$router->get('/income-evaluator', function() {
    $controller = new IncomeEvaluatorController();
    $controller->index();
});

$router->get('/income-evaluator/create', function() {
    $controller = new IncomeEvaluatorController();
    $controller->create();
});

$router->post('/income-evaluator/store', function() {
    $controller = new IncomeEvaluatorController();
    $controller->store();
});

$router->get('/income-evaluator/compare/:id', function($id) {
    $controller = new IncomeEvaluatorController();
    $controller->compare($id);
});

$router->post('/income-evaluator/save-scores/:id', function($id) {
    $controller = new IncomeEvaluatorController();
    $controller->saveScores($id);
});

$router->get('/income-evaluator/results/:id', function($id) {
    $controller = new IncomeEvaluatorController();
    $controller->results($id);
});

$router->post('/income-evaluator/delete/:id', function($id) {
    $controller = new IncomeEvaluatorController();
    $controller->delete($id);
});

// AI Agents Army routes
$router->get('/ai-agents', function() {
    $controller = new AIAgentController();
    $controller->index();
});

$router->get('/ai-agents/view/:id', function($id) {
    $controller = new AIAgentController();
    $controller->viewAgent($id);
});

$router->get('/ai-agents/create', function() {
    $controller = new AIAgentController();
    $controller->createAgent();
});

$router->post('/ai-agents/store', function() {
    $controller = new AIAgentController();
    $controller->storeAgent();
});

$router->get('/ai-agents/edit/:id', function($id) {
    $controller = new AIAgentController();
    $controller->editAgent($id);
});

$router->post('/ai-agents/update/:id', function($id) {
    $controller = new AIAgentController();
    $controller->updateAgent($id);
});

$router->get('/ai-agents/delete/:id', function($id) {
    $controller = new AIAgentController();
    $controller->deleteAgent($id);
});

// AI Agent Categories routes
$router->get('/ai-agents/categories', function() {
    $controller = new AIAgentController();
    $controller->categories();
});

$router->get('/ai-agents/categories/create', function() {
    $controller = new AIAgentController();
    $controller->createCategory();
});

$router->post('/ai-agents/categories/store', function() {
    $controller = new AIAgentController();
    $controller->storeCategory();
});

$router->get('/ai-agents/categories/edit/:id', function($id) {
    $controller = new AIAgentController();
    $controller->editCategory($id);
});

$router->post('/ai-agents/categories/update/:id', function($id) {
    $controller = new AIAgentController();
    $controller->updateCategory($id);
});

$router->get('/ai-agents/categories/delete/:id', function($id) {
    $controller = new AIAgentController();
    $controller->deleteCategory($id);
});

// AI Agent Tasks routes
$router->get('/ai-agents/tasks/create', function() {
    $controller = new AIAgentController();
    $controller->createTask();
});

$router->post('/ai-agents/tasks/create', function() {
    $controller = new AIAgentController();
    $controller->storeTask();
});

$router->get('/ai-agents/tasks/view/:id', function($id) {
    $controller = new AIAgentController();
    $controller->viewTask($id);
});

$router->get('/ai-agents/tasks/edit/:id', function($id) {
    $controller = new AIAgentController();
    $controller->editTask($id);
});

$router->post('/ai-agents/tasks/edit/:id', function($id) {
    $controller = new AIAgentController();
    $controller->updateTask($id);
});

$router->post('/ai-agents/tasks/update-status/:id', function($id) {
    $controller = new AIAgentController();
    $controller->updateTaskStatus($id);
});

$router->post('/ai-agents/tasks/start/:id', function($id) {
    $controller = new AIAgentController();
    $controller->startTask($id);
});

$router->post('/ai-agents/tasks/complete/:id', function($id) {
    $controller = new AIAgentController();
    $controller->completeTask($id);
});

// Medical Test Reports routes
$router->get('/medical', function() {
    $controller = new MedicalController();
    $controller->index();
});

$router->get('/medical/reports', function() {
    $controller = new MedicalController();
    $controller->reports();
});

$router->get('/medical/reports/new', function() {
    $controller = new MedicalController();
    $controller->newReport();
});

$router->post('/medical/reports/save', function() {
    $controller = new MedicalController();
    $controller->saveReport();
});

$router->get('/medical/reports/view/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewReport($id);
});

$router->get('/medical/reports/edit/:id', function($id) {
    $controller = new MedicalController();
    $controller->editReport($id);
});

$router->post('/medical/reports/update/:id', function($id) {
    $controller = new MedicalController();
    $controller->updateReport($id);
});

$router->get('/medical/reports/delete/:id', function($id) {
    $controller = new MedicalController();
    $controller->deleteReport($id);
});

$router->get('/medical/reports/parameter/:name', function($name) {
    $controller = new MedicalController();
    $controller->parameterHistory($name);
});

$router->get('/medical/reports/generate', function() {
    $controller = new MedicalController();
    $controller->generateReports();
});

// Medical Medication Tracker routes
$router->get('/medical/medication', function() {
    $controller = new MedicalController();
    $controller->medications();
});

$router->get('/medical/medication/new', function() {
    $controller = new MedicalController();
    $controller->newMedication();
});

$router->post('/medical/medication/save', function() {
    $controller = new MedicalController();
    $controller->saveMedication();
});

$router->get('/medical/medication/view/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewMedication($id);
});

$router->get('/medical/medication/edit/:id', function($id) {
    $controller = new MedicalController();
    $controller->editMedication($id);
});

$router->post('/medical/medication/update/:id', function($id) {
    $controller = new MedicalController();
    $controller->updateMedication($id);
});

$router->get('/medical/medication/delete/:id', function($id) {
    $controller = new MedicalController();
    $controller->deleteMedication($id);
});

$router->get('/medical/medication/log', function() {
    $controller = new MedicalController();
    $controller->logMedication();
});

$router->get('/medical/medication/log/:id', function($id) {
    $controller = new MedicalController();
    $controller->logMedicationById($id);
});

$router->post('/medical/medication/log/save', function() {
    $controller = new MedicalController();
    $controller->saveMedicationLog();
});

$router->get('/medical/medication/log/edit/:id', function($id) {
    $controller = new MedicalController();
    $controller->editMedicationLog($id);
});

$router->post('/medical/medication/log/update/:id', function($id) {
    $controller = new MedicalController();
    $controller->updateMedicationLog($id);
});

$router->get('/medical/medication/log/delete/:id', function($id) {
    $controller = new MedicalController();
    $controller->deleteMedicationLog($id);
});

$router->get('/medical/medication/reminder/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newMedicationReminder($id);
});

$router->post('/medical/medication/reminder/save', function() {
    $controller = new MedicalController();
    $controller->saveMedicationReminder();
});

$router->get('/medical/medication/reminder/edit/:id', function($id) {
    $controller = new MedicalController();
    $controller->editMedicationReminder($id);
});

// Pet Management routes
$router->get('/medical/pets', function() {
    $controller = new MedicalController();
    $controller->pets();
});

$router->get('/medical/pets/new', function() {
    $controller = new MedicalController();
    $controller->newPet();
});

$router->post('/medical/pets/save', function() {
    $controller = new MedicalController();
    $controller->savePet();
});

$router->get('/medical/pets/view/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewPet($id);
});

$router->get('/medical/pets/health-dashboard/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewPetHealthDashboard($id);
});

$router->get('/medical/pets/edit/:id', function($id) {
    $controller = new MedicalController();
    $controller->editPet($id);
});

$router->post('/medical/pets/update/:id', function($id) {
    $controller = new MedicalController();
    $controller->updatePet($id);
});

$router->get('/medical/pets/delete/:id', function($id) {
    $controller = new MedicalController();
    $controller->deletePet($id);
});

// Pet Medication routes
$router->get('/medical/pets/medication/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetMedication($id);
});

$router->post('/medical/pets/medication/save', function() {
    $controller = new MedicalController();
    $controller->savePetMedication();
});

// Pet Treatment routes
$router->get('/medical/pets/treatment/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetTreatment($id);
});

$router->post('/medical/pets/treatment/save', function() {
    $controller = new MedicalController();
    $controller->savePetTreatment();
});

// Pet Training routes
$router->get('/medical/pets/training/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetTraining($id);
});

$router->post('/medical/pets/training/save', function() {
    $controller = new MedicalController();
    $controller->savePetTraining();
});

// Pet Vitals routes
$router->get('/medical/pets/vitals/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetVitals($id);
});

$router->post('/medical/pets/vitals/save', function() {
    $controller = new MedicalController();
    $controller->savePetVitals();
});

$router->get('/medical/pets/vitals/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewPetVitals($id);
});

// Pet Health Assessment routes
$router->get('/medical/pets/health-assessment/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetHealthAssessment($id);
});

$router->post('/medical/pets/health-assessment/save', function() {
    $controller = new MedicalController();
    $controller->savePetHealthAssessment();
});

$router->get('/medical/pets/health-assessments/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewPetHealthAssessments($id);
});

// Pet Diet Plan routes
$router->get('/medical/pets/diet-plan/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetDietPlan($id);
});

$router->post('/medical/pets/diet-plan/save', function() {
    $controller = new MedicalController();
    $controller->savePetDietPlan();
});

// Pet Meal routes
$router->get('/medical/pets/meal/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetMeal($id);
});

$router->post('/medical/pets/meal/save', function() {
    $controller = new MedicalController();
    $controller->savePetMeal();
});

// Pet Food Allergy routes
$router->get('/medical/pets/food-allergy/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetFoodAllergy($id);
});

$router->post('/medical/pets/food-allergy/save', function() {
    $controller = new MedicalController();
    $controller->savePetFoodAllergy();
});

// Pet Activity routes
$router->get('/medical/pets/activity/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetActivity($id);
});

$router->post('/medical/pets/activity/save', function() {
    $controller = new MedicalController();
    $controller->savePetActivity();
});

// Pet Behavior routes
$router->get('/medical/pets/behavior/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetBehavior($id);
});

$router->post('/medical/pets/behavior/save', function() {
    $controller = new MedicalController();
    $controller->savePetBehavior();
});

// Pet Vet Visit routes
$router->get('/medical/pets/vet-visit/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetVetVisit($id);
});

$router->post('/medical/pets/vet-visit/save', function() {
    $controller = new MedicalController();
    $controller->savePetVetVisit();
});

$router->get('/medical/pets/vet-visits/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewPetVetVisits($id);
});

$router->get('/medical/pets/vet-visit/view/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewPetVetVisit($id);
});

$router->get('/medical/pets/vet-visit/edit/:id', function($id) {
    $controller = new MedicalController();
    $controller->editPetVetVisit($id);
});

$router->post('/medical/pets/vet-visit/update', function() {
    $controller = new MedicalController();
    $controller->updatePetVetVisit();
});

$router->get('/medical/pets/vet-visit/delete/:id', function($id) {
    $controller = new MedicalController();
    $controller->deletePetVetVisit($id);
});

// Pet Expense routes
$router->get('/medical/pets/expense/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetExpense($id);
});

$router->post('/medical/pets/expense/save', function() {
    $controller = new MedicalController();
    $controller->savePetExpense();
});

// Pet Document routes
$router->get('/medical/pets/document/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetDocument($id);
});

$router->post('/medical/pets/document/save', function() {
    $controller = new MedicalController();
    $controller->savePetDocument();
});

// Pet Contact routes
$router->get('/medical/pets/contact/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetContact($id);
});

$router->post('/medical/pets/contact/save', function() {
    $controller = new MedicalController();
    $controller->savePetContact();
});

// Pet Reminder routes
$router->get('/medical/pets/reminder/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetReminder($id);
});

$router->post('/medical/pets/reminder/save', function() {
    $controller = new MedicalController();
    $controller->savePetReminder();
});

$router->get('/medical/pets/reminders/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewPetReminders($id);
});

$router->get('/medical/pets/vaccination-schedule/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewPetVaccinationSchedule($id);
});

// Pet Growth Tracking routes
$router->get('/medical/pets/growth/:id', function($id) {
    $controller = new MedicalController();
    $controller->viewPetGrowthDashboard($id);
});

$router->get('/medical/pets/growth/record/:id', function($id) {
    $controller = new MedicalController();
    $controller->recordPetGrowth($id);
});

$router->post('/medical/pets/growth/save', function() {
    $controller = new MedicalController();
    $controller->savePetGrowth();
});

$router->get('/medical/pets/growth/edit/:id', function($id) {
    $controller = new MedicalController();
    $controller->editPetGrowth($id);
});

$router->post('/medical/pets/growth/update', function() {
    $controller = new MedicalController();
    $controller->updatePetGrowth();
});

$router->get('/medical/pets/growth/delete/:id', function($id) {
    $controller = new MedicalController();
    $controller->deletePetGrowth($id);
});

// Pet Growth Milestone routes
$router->get('/medical/pets/growth/milestone/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetGrowthMilestone($id);
});

$router->post('/medical/pets/growth/milestone/save', function() {
    $controller = new MedicalController();
    $controller->savePetGrowthMilestone();
});

$router->get('/medical/pets/growth/milestone/edit/:id', function($id) {
    $controller = new MedicalController();
    $controller->editPetGrowthMilestone($id);
});

$router->post('/medical/pets/growth/milestone/update', function() {
    $controller = new MedicalController();
    $controller->updatePetGrowthMilestone();
});

$router->get('/medical/pets/growth/milestone/delete/:id', function($id) {
    $controller = new MedicalController();
    $controller->deletePetGrowthMilestone($id);
});

$router->get('/medical/pets/reminder/edit/:id', function($id) {
    $controller = new MedicalController();
    $controller->editPetReminder($id);
});

$router->post('/medical/pets/reminder/update', function() {
    $controller = new MedicalController();
    $controller->updatePetReminder();
});

$router->get('/medical/pets/reminder/complete/:id', function($id) {
    $controller = new MedicalController();
    $controller->completePetReminder($id);
});

$router->get('/medical/pets/reminder/delete/:id', function($id) {
    $controller = new MedicalController();
    $controller->deletePetReminder($id);
});

// Pet Breeding routes
$router->get('/medical/pets/breeding/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetBreeding($id);
});

$router->post('/medical/pets/breeding/save', function() {
    $controller = new MedicalController();
    $controller->savePetBreeding();
});

// Pet Offspring routes
$router->get('/medical/pets/offspring/new/:id', function($id) {
    $controller = new MedicalController();
    $controller->newPetOffspring($id);
});

$router->post('/medical/pets/offspring/save', function() {
    $controller = new MedicalController();
    $controller->savePetOffspring();
});

$router->post('/medical/medication/reminder/update/:id', function($id) {
    $controller = new MedicalController();
    $controller->updateMedicationReminder($id);
});

$router->get('/medical/medication/reminder/delete/:id', function($id) {
    $controller = new MedicalController();
    $controller->deleteMedicationReminder($id);
});

$router->get('/medical/medication/reports', function() {
    $controller = new MedicalController();
    $controller->medicationReports();
});

// 404 route
$router->notFound(function() {
    http_response_code(404);
    echo '<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>404 - Page Not Found</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    </head>
    <body class="bg-gray-100 h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
            <h1 class="text-4xl font-bold text-red-500 mb-4">404</h1>
            <h2 class="text-2xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
            <p class="text-gray-600 mb-6">The page you are looking for doesn\'t exist or has been moved.</p>
            <a href="/momentum" class="inline-block bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded transition duration-200">
                Go Home
            </a>
        </div>
    </body>
    </html>';
});

// Include freelance routes
require_once BASE_PATH . '/src/routes/freelance_routes.php';

// Include brigade routes
require_once BASE_PATH . '/src/routes/brigade_routes.php';

// Include checklist routes
require_once BASE_PATH . '/src/routes/checklist_routes.php';

// Include YouTube Agent routes
try {
    require_once BASE_PATH . '/src/routes/youtube_agent_routes.php';
} catch (Exception $e) {
    // Log the error but don't let it crash the main site
    error_log('Error loading YouTube Agent routes: ' . $e->getMessage());
}

// Include Clone routes
try {
    require_once BASE_PATH . '/src/routes/clone_routes.php';
} catch (Exception $e) {
    // Log the error but don't let it crash the main site
    error_log('Error loading Clone routes: ' . $e->getMessage());
}

// Include AI Prompt routes
try {
    require_once BASE_PATH . '/src/routes/ai_prompt_routes.php';
} catch (Exception $e) {
    // Log the error but don't let it crash the main site
    error_log('Error loading AI Prompt routes: ' . $e->getMessage());
}

// Include Quick Capture routes
try {
    require_once BASE_PATH . '/src/routes/quick_capture_routes.php';
} catch (Exception $e) {
    // Log the error but don't let it crash the main site
    error_log('Error loading Quick Capture routes: ' . $e->getMessage());
}

// Research & Planning routes
$router->get('/research', function() {
    $controller = new ResearchPlanningController();
    $controller->index();
});

$router->get('/research/dashboard', function() {
    $controller = new ResearchPlanningController();
    $controller->index();
});

$router->get('/research/create-session', function() {
    $controller = new ResearchPlanningController();
    $controller->createSession();
});

$router->post('/research/create-session', function() {
    $controller = new ResearchPlanningController();
    $controller->createSession();
});

$router->get('/research/session/:id', function($id) {
    $controller = new ResearchPlanningController();
    $controller->viewSession($id);
});

$router->post('/research/session/:id/add-link', function($id) {
    $controller = new ResearchPlanningController();
    $controller->addLink($id);
});

$router->get('/research/session/:id/create-plan', function($id) {
    $controller = new ResearchPlanningController();
    $controller->createPlan($id);
});

$router->post('/research/session/:id/create-plan', function($id) {
    $controller = new ResearchPlanningController();
    $controller->createPlan($id);
});

$router->get('/research/plan/:id', function($id) {
    $controller = new ResearchPlanningController();
    $controller->viewPlan($id);
});

$router->get('/research/search', function() {
    $controller = new ResearchPlanningController();
    $controller->search();
});

$router->get('/research/templates', function() {
    $controller = new ResearchPlanningController();
    $controller->templates();
});

$router->get('/research/knowledge-base', function() {
    $controller = new ResearchPlanningController();
    $controller->knowledgeBase();
});

// Online Business Dashboard routes
$router->get('/online-business', function() {
    $controller = new OnlineBusinessController();
    $controller->index();
});

$router->get('/online-business/create', function() {
    $controller = new OnlineBusinessController();
    $controller->create();
});

$router->post('/online-business/store', function() {
    $controller = new OnlineBusinessController();
    $controller->store();
});

$router->get('/online-business/dashboard/:id', function($id) {
    $controller = new OnlineBusinessController();
    $controller->dashboard($id);
});

$router->get('/online-business/edit/:id', function($id) {
    $controller = new OnlineBusinessController();
    $controller->edit($id);
});

$router->post('/online-business/update/:id', function($id) {
    $controller = new OnlineBusinessController();
    $controller->update($id);
});

$router->get('/online-business/delete/:id', function($id) {
    $controller = new OnlineBusinessController();
    $controller->delete($id);
});

$router->get('/online-business/metrics/:id', function($id) {
    $controller = new OnlineBusinessController();
    $controller->metrics($id);
});

$router->post('/online-business/save-metrics/:id', function($id) {
    $controller = new OnlineBusinessController();
    $controller->saveMetrics($id);
});

// Remove the online_business_routes.php file reference since we're using direct controller calls

// Dispatch the request
$router->dispatch();
