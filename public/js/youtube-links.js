/**
 * YouTube Links JavaScript
 * Handles YouTube links management functionality
 */

let bulkModeActive = false;
let selectedLinks = new Set();

document.addEventListener('DOMContentLoaded', function() {
    initializeYouTubeLinks();
});

function initializeYouTubeLinks() {
    // Initialize quick add form
    const quickAddForm = document.getElementById('quickAddForm');
    if (quickAddForm) {
        quickAddForm.addEventListener('submit', handleQuickAdd);
    }

    // Initialize link checkboxes
    const linkCheckboxes = document.querySelectorAll('.link-checkbox');
    linkCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleCheckboxChange);
    });

    // Initialize keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

function openQuickAddModal() {
    const modal = document.getElementById('quickAddModal');
    if (modal) {
        modal.classList.remove('hidden');
        document.getElementById('quickUrl').focus();
    }
}

function closeQuickAddModal() {
    const modal = document.getElementById('quickAddModal');
    if (modal) {
        modal.classList.add('hidden');
        document.getElementById('quickAddForm').reset();
    }
}

async function handleQuickAdd(e) {
    e.preventDefault();
    
    const url = document.getElementById('quickUrl').value;
    const collectionId = document.getElementById('quickCollection').value;
    const priority = document.getElementById('quickPriority').value;

    if (!url) {
        showNotification('Please enter a YouTube URL', 'error');
        return;
    }

    try {
        const response = await fetch('/momentum/tools/youtube-links/quick-add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: url,
                collection_id: collectionId,
                priority: priority
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Link added successfully!', 'success');
            closeQuickAddModal();
            // Refresh the page to show the new link
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(result.message || 'Failed to add link', 'error');
        }
    } catch (error) {
        console.error('Error adding link:', error);
        showNotification('An error occurred while adding the link', 'error');
    }
}

async function updateLinkStatus(linkId, status, progress = null) {
    try {
        const response = await fetch('/momentum/tools/youtube-links/update-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                link_id: linkId,
                status: status,
                progress: progress
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Status updated successfully!', 'success');
            // Update the UI
            updateLinkStatusUI(linkId, status);
        } else {
            showNotification(result.message || 'Failed to update status', 'error');
        }
    } catch (error) {
        console.error('Error updating status:', error);
        showNotification('An error occurred while updating status', 'error');
    }
}

function updateLinkStatusUI(linkId, status) {
    const linkElement = document.querySelector(`[data-link-id="${linkId}"]`);
    if (!linkElement) return;

    const statusBadge = linkElement.querySelector('.inline-flex.items-center.px-2.py-1.rounded-full');
    if (!statusBadge) return;

    // Remove existing status classes
    statusBadge.className = statusBadge.className.replace(/bg-\w+-\d+/g, '').replace(/text-\w+-\d+/g, '');

    // Add new status classes and text
    let statusClasses = '';
    let statusText = '';

    switch (status) {
        case 'completed':
            statusClasses = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            statusText = 'Completed';
            break;
        case 'watching':
            statusClasses = 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
            statusText = 'Watching';
            break;
        case 'to_watch':
        default:
            statusClasses = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
            statusText = 'To Watch';
            break;
    }

    statusBadge.className += ' ' + statusClasses;
    statusBadge.textContent = statusText;
}

function toggleBulkMode() {
    bulkModeActive = !bulkModeActive;
    const checkboxes = document.querySelectorAll('.bulk-checkbox');
    const bulkBar = document.getElementById('bulk-actions-bar');

    if (bulkModeActive) {
        checkboxes.forEach(checkbox => checkbox.classList.remove('hidden'));
        bulkBar.classList.remove('hidden');
    } else {
        checkboxes.forEach(checkbox => {
            checkbox.classList.add('hidden');
            checkbox.querySelector('input').checked = false;
        });
        bulkBar.classList.add('hidden');
        selectedLinks.clear();
        updateSelectedCount();
    }
}

function cancelBulkMode() {
    toggleBulkMode();
}

function handleCheckboxChange(e) {
    const linkId = e.target.value;
    
    if (e.target.checked) {
        selectedLinks.add(linkId);
    } else {
        selectedLinks.delete(linkId);
    }
    
    updateSelectedCount();
}

function updateSelectedCount() {
    const countElement = document.getElementById('selected-count');
    if (countElement) {
        countElement.textContent = selectedLinks.size;
    }
}

async function bulkAction(action) {
    if (selectedLinks.size === 0) {
        showNotification('Please select at least one link', 'warning');
        return;
    }

    try {
        const response = await fetch('/momentum/tools/youtube-links/bulk-action', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                link_ids: Array.from(selectedLinks),
                action: action
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Bulk action completed successfully!', 'success');
            // Refresh the page to show updates
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(result.message || 'Failed to complete bulk action', 'error');
        }
    } catch (error) {
        console.error('Error performing bulk action:', error);
        showNotification('An error occurred while performing bulk action', 'error');
    }
}

function handleKeyboardShortcuts(e) {
    // Only handle shortcuts when not typing in an input
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
    }

    switch (e.key.toLowerCase()) {
        case 'a':
            e.preventDefault();
            openQuickAddModal();
            break;
        case 'b':
            e.preventDefault();
            toggleBulkMode();
            break;
        case 'escape':
            if (document.getElementById('quickAddModal').classList.contains('hidden') === false) {
                closeQuickAddModal();
            } else if (bulkModeActive) {
                cancelBulkMode();
            }
            break;
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${getNotificationClasses(type)}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${getNotificationIcon(type)} mr-3"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg">&times;</button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function getNotificationClasses(type) {
    switch (type) {
        case 'success':
            return 'bg-green-100 border border-green-400 text-green-700';
        case 'error':
            return 'bg-red-100 border border-red-400 text-red-700';
        case 'warning':
            return 'bg-yellow-100 border border-yellow-400 text-yellow-700';
        default:
            return 'bg-blue-100 border border-blue-400 text-blue-700';
    }
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success':
            return 'fa-check-circle';
        case 'error':
            return 'fa-exclamation-circle';
        case 'warning':
            return 'fa-exclamation-triangle';
        default:
            return 'fa-info-circle';
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('quickAddModal');
    if (modal && e.target === modal) {
        closeQuickAddModal();
    }
});
